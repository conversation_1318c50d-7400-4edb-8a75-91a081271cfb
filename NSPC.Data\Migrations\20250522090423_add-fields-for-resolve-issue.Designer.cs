﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NPSC.Data;
using NSPC.Data;
using NSPC.Data.Data;
using NSPC.Data.Data.Entity.Contract;
using NSPC.Data.Data.Entity.JsonbEntity;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NSPC.Data.Migrations
{
    [DbContext(typeof(SMDbContext))]
    [Migration("20250522090423_add-fields-for-resolve-issue")]
    partial class addfieldsforresolveissue
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.27")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.HasSequence<int>("AssetLiquidationSheetSequence");

            modelBuilder.HasSequence<int>("AssetMaintenanceSheetSequence");

            modelBuilder.HasSequence<int>("AssetSequence");

            modelBuilder.Entity("FileManagement.Data.fm_Search_Sample", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("QueryJsonString")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("fm_Search_Sample");
                });

            modelBuilder.Entity("NSPC.Data.bsd_KeyValue", b =>
                {
                    b.Property<string>("Key")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Key");

                    b.ToTable("bsd_Key_Value");
                });

            modelBuilder.Entity("NSPC.Data.BsdNavigation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("HasChild")
                        .HasColumnType("boolean");

                    b.Property<string>("IconClass")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("IdPath")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<Guid>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(900)
                        .HasColumnType("character varying(900)");

                    b.Property<string>("QueryParams")
                        .HasColumnType("text");

                    b.Property<bool?>("Status")
                        .HasColumnType("boolean");

                    b.Property<string>("SubUrl")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int?>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UrlRewrite")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("TenantId");

                    b.ToTable("bsd_Navigation");
                });

            modelBuilder.Entity("NSPC.Data.BsdNavigationMapRole", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("FromSubNavigation")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("NavigationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("NavigationId");

                    b.HasIndex("TenantId");

                    b.ToTable("bsd_Navigation_Map_Role");
                });

            modelBuilder.Entity("NSPC.Data.BsdParameter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("GroupCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("boolean");

                    b.Property<Guid>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("bsd_Parameter");
                });

            modelBuilder.Entity("NSPC.Data.cata_Commune", b =>
                {
                    b.Property<int>("CommuneCode")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("CommuneCode"));

                    b.Property<string>("CommuneName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("integer");

                    b.Property<int>("DistrictId_VP")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("OldVTPName")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("VNPAsciiName")
                        .HasColumnType("text");

                    b.Property<string>("VTPAsciiName")
                        .HasColumnType("text");

                    b.Property<int>("VnpostSyncStatus")
                        .HasColumnType("integer");

                    b.Property<int>("WardId_VP")
                        .HasColumnType("integer");

                    b.Property<string>("WardName_VP")
                        .HasColumnType("text");

                    b.HasKey("CommuneCode");

                    b.HasIndex("TenantId");

                    b.ToTable("cata_Commune");
                });

            modelBuilder.Entity("NSPC.Data.cata_District", b =>
                {
                    b.Property<int>("DistrictCode")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("DistrictCode"));

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DelayShipFromDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DelayShipToDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DistrictId_VP")
                        .HasColumnType("integer");

                    b.Property<string>("DistrictName")
                        .HasColumnType("text");

                    b.Property<string>("DistrictName_VP")
                        .HasColumnType("text");

                    b.Property<int>("DistrictShippingType")
                        .HasColumnType("integer");

                    b.Property<string>("DistrictValue_VP")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("integer");

                    b.Property<int>("ProvinceId_VP")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StopShipFromDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("StopShipToDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("VnpostSyncStatus")
                        .HasColumnType("integer");

                    b.HasKey("DistrictCode");

                    b.HasIndex("TenantId");

                    b.ToTable("cata_District");
                });

            modelBuilder.Entity("NSPC.Data.cata_Province", b =>
                {
                    b.Property<int>("ProvinceCode")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("ProvinceCode"));

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DelayShipFromDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DelayShipToDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("ProvinceCode_VP")
                        .HasColumnType("text");

                    b.Property<int>("ProvinceId_VP")
                        .HasColumnType("integer");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceName_VP")
                        .HasColumnType("text");

                    b.Property<int>("ProvinceShippingType")
                        .HasColumnType("integer");

                    b.Property<int>("ServiceType_VP")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StopShipFromDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("StopShipToDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalDistrictShipDelay")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDistrictShipStop")
                        .HasColumnType("integer");

                    b.HasKey("ProvinceCode");

                    b.HasIndex("TenantId");

                    b.ToTable("cata_Province");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ActivityHistory.sm_ActiviyHisroty", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ActiviyHisroty");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<List<jsonb_AdvanceRequestHistory>>("AdvanceRequestHistories")
                        .HasColumnType("jsonb");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("PriorityLevelCode")
                        .HasColumnType("text");

                    b.Property<string>("PriorityLevelColor")
                        .HasColumnType("text");

                    b.Property<string>("PriorityLevelName")
                        .HasColumnType("text");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("StatusColor")
                        .HasColumnType("text");

                    b.Property<string>("StatusName")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalLineAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VatPercent")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AdvanceRequest");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequestItems", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdvancePurpose")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("sm_AdvanceRequestId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("sm_AdvanceRequestId");

                    b.ToTable("sm_AdvanceRequestItems");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_Asset", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AccumulatedDepreciation")
                        .HasColumnType("numeric");

                    b.Property<Guid>("AssetLocationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetTypeId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValueSql("CONCAT('AN-', LPAD(NEXTVAL('\"AssetSequence\"')::text, 5, '0'))");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DepreciationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("DepreciationTime")
                        .HasColumnType("integer");

                    b.Property<int>("DepreciationUnit")
                        .HasColumnType("integer");

                    b.Property<decimal?>("DepreciationValue")
                        .HasColumnType("numeric");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<List<jsonb_Attachment>>("Documents")
                        .HasColumnType("jsonb");

                    b.Property<List<jsonb_Attachment>>("Images")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("ManufactureDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("MeasureUnitId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OriginBrand")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("PurchasedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("RemainingValue")
                        .HasColumnType("numeric");

                    b.Property<string>("Serial")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetLocationId");

                    b.HasIndex("AssetTypeId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("MeasureUnitId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_Asset");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_AssetLiquidationSheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValueSql("CONCAT('LSN-', LPAD(NEXTVAL('\"AssetLiquidationSheetSequence\"')::text, 5, '0'))");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DecisionNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("LiquidationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LiquidationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("LiquidationValue")
                        .HasColumnType("numeric(18,2)");

                    b.Property<Guid?>("LiquidatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("LiquidatorId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetLiquidationSheet");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_AssetMaintenanceSheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValueSql("CONCAT('MSN-', LPAD(NEXTVAL('\"AssetMaintenanceSheetSequence\"')::text, 5, '0'))");

                    b.Property<DateTime?>("CompleteDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("EstimatedCost")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaintenanceContent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("MaintenanceLocation")
                        .HasColumnType("integer");

                    b.Property<DateTime>("MaintenancePeriod")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaintenancePlace")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("integer");

                    b.Property<Guid?>("PerformerId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("PerformerId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetMaintenanceSheet");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetAllocation.sm_AssetAllocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("ExecutionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("FromLocationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FromUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Operation")
                        .HasColumnType("integer");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ToLocationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ToUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FromLocationId");

                    b.HasIndex("FromUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TenantId");

                    b.HasIndex("ToLocationId");

                    b.HasIndex("ToUserId");

                    b.ToTable("sm_AssetAllocation");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_AssetGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetGroup");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_AssetType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetGroupId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetGroupId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetType");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_MeasureUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_MeasureUnit");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetHistory.sm_AssetUsageHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uuid");

                    b.Property<int>("AssetStatus")
                        .HasColumnType("integer");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("ExecutionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Operation")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("LocationId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_AssetUsageHistory");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetIncident.sm_AssetIncident", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("CompensationAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("IncidentType")
                        .HasColumnType("integer");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetIncident");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("ParentId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_AssetLocation");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangLuongItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("BhtnNLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BhtnNSDLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BhxhNLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BhxhNSDLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BhytNLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BhytNSDLD")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("CacKhoanTroCapId")
                        .HasColumnType("uuid");

                    b.Property<string>("ChucVu")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<decimal?>("GiamTruBanThan")
                        .HasColumnType("numeric");

                    b.Property<Guid>("IdBangTinhLuong")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("Luong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("LuongCoBan")
                        .HasColumnType("numeric");

                    b.Property<string>("MaSo")
                        .HasColumnType("text");

                    b.Property<decimal?>("NgayCong")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("NgayNhan")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<decimal?>("SoNguoiPhuThuoc")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("SoTienPhuThuoc")
                        .HasColumnType("numeric");

                    b.Property<string>("TenNhanVien")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("ThuNhapCaNhan")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ThuNhapTinhThue")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ThucLinh")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ThueTNCNPhaiNop")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Tong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongNLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongNSDLD")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongTatCa")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("CacKhoanTroCapId");

                    b.HasIndex("IdBangTinhLuong");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_BangLuongItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangTinhLuong", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DiaChiCongTy")
                        .HasColumnType("text");

                    b.Property<Guid>("IdBangChamCongActive")
                        .HasColumnType("uuid");

                    b.Property<bool>("KichHoatBangLuong")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaSoThue")
                        .HasColumnType("text");

                    b.Property<int>("SoNgayCongTrongThang")
                        .HasColumnType("integer");

                    b.Property<string>("TenBangTinhLuong")
                        .HasColumnType("text");

                    b.Property<string>("TenCongTy")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ThoiGian")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_BangTinhLuong");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_CacKhoanTroCap", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AnCa")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("DienThoai")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TrangPhuc")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_CacKhoanTroCap");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BHXH.mk_BHXH", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("BHTNNguoiLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BHTNNguoiSuDungLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BHXHNguoiLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BHXHNguoiSuDungLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BHYTNguoiLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BHYTNguoiSuDungLaoDong")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TongNguoiLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongNguoiSuDungLaoDong")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongTatCa")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_BHXH");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.mk_Bom", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("IdSanPham")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TongTien")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TongTienThanhToan")
                        .HasColumnType("numeric");

                    b.Property<double>("VAT")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("IdSanPham");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_Bom");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.sm_Bom", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_OtherCost>>("ListOtherCost")
                        .HasColumnType("jsonb");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SubTotalAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalOtherExpenses")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalVatAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Bom");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.sm_Materials", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BomId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<decimal>("LineVatAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("LineVatPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("MaterialCode")
                        .HasColumnType("text");

                    b.Property<Guid>("MaterialId")
                        .HasColumnType("uuid");

                    b.Property<string>("MaterialName")
                        .HasColumnType("text");

                    b.Property<string>("MaterialUnit")
                        .HasColumnType("text");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("BomId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Materials");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CashbookTransaction.sm_Cashbook_Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AdvanceRequestId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<List<jsonb_Attachment>>("Attachments")
                        .HasColumnType("jsonb");

                    b.Property<decimal?>("ClosingBanlance")
                        .HasColumnType("numeric");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EntityCode")
                        .HasColumnType("text");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityName")
                        .HasColumnType("text");

                    b.Property<string>("EntityTypeCode")
                        .HasColumnType("text");

                    b.Property<string>("EntityTypeName")
                        .HasColumnType("text");

                    b.Property<string>("EntityUrl")
                        .HasColumnType("text");

                    b.Property<string>("IsActive")
                        .HasColumnType("text");

                    b.Property<bool>("IsDebt")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal?>("OpeningBalance")
                        .HasColumnType("numeric");

                    b.Property<string>("OriginalDocumentCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("OriginalDocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalDocumentType")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethodCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("PurposeCode")
                        .HasColumnType("text");

                    b.Property<string>("PurposeName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReceiptDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Reference")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionTypeCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AdvanceRequestId");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Cashbook_Transaction");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CauHinhNhanSu.mk_CauHinhNhanSu", b =>
                {
                    b.Property<Guid?>("IdUser")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AnCa")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("DienThoai")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("LuongCoBan")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TrangPhuc")
                        .HasColumnType("numeric");

                    b.HasKey("IdUser");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_CauHinhNhanSu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCong", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Cong")
                        .HasColumnType("integer");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("KichHoatBangChamCong")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Nam")
                        .HasColumnType("integer");

                    b.Property<string>("TenBangChamCong")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Thang")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_ChamCong");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCongItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AnCa")
                        .HasColumnType("numeric");

                    b.Property<string>("ChucVu")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("DienThoai")
                        .HasColumnType("numeric");

                    b.Property<string>("HoVaTen")
                        .HasColumnType("text");

                    b.Property<Guid>("IdChamCong")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("LamThemChuNhat")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("LamThemNgayLe")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("LamThemNgayThuong")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("LuongCoBan")
                        .HasColumnType("numeric");

                    b.Property<string>("MaSo")
                        .HasColumnType("text");

                    b.Property<decimal?>("NgayCong")
                        .HasColumnType("numeric");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TrangPhuc")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("IdChamCong");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_ChamCongItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.NgayTrongThang", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Cong")
                        .HasColumnType("text");

                    b.Property<string>("LoaiNgay")
                        .HasColumnType("text");

                    b.Property<int>("Ngay")
                        .HasColumnType("integer");

                    b.Property<string>("ThuTrongTuan")
                        .HasColumnType("text");

                    b.Property<Guid?>("mk_ChamCongItemId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("mk_ChamCongItemId");

                    b.ToTable("NgayTrongThang");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChucVu.mk_ChucVu", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaChucVu")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenChucVu")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_ChucVu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CodeType.sm_CodeType_Item", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CodeTypeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("IconClass")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CodeTypeId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_CodeType_Item");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Contract.sm_Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AcceptanceValueBeforeVatAmount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int?>("AcceptanceYear")
                        .HasColumnType("integer");

                    b.Property<List<ContractAppendixItem>>("Appendices")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("AssignmentAYear")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ConsultingServiceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ExpectedAcceptanceMonth")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ExpectedApprovalMonth")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<decimal?>("ExpectedVolume")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime?>("InvoiceIssuanceDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("PaidAmount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<decimal?>("SettlementValueAmount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<float?>("TaxRatePercentage")
                        .HasColumnType("real");

                    b.Property<Guid>("TemplateStageId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("ValueBeforeVatAmount")
                        .HasColumnType("numeric(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ConsultingServiceId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("TemplateStageId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Contract");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.DebtTransaction.sm_DebtTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<decimal>("ChangeAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("DebtAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("EntityCode")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EntityType")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("OriginalDocumentCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("OriginalDocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalDocumentType")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_DebtTransaction");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.DuAn.mk_DuAn", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaDuAn")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TenDuAn")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TongHopChi")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TongHopThu")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_DuAn");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BuyerAddress")
                        .HasColumnType("text");

                    b.Property<string>("BuyerBankAccount")
                        .HasColumnType("text");

                    b.Property<string>("BuyerBankName")
                        .HasColumnType("text");

                    b.Property<string>("BuyerName")
                        .HasColumnType("text");

                    b.Property<string>("BuyerPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("BuyerTaxCode")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_PaymentInvoice>>("ListOfPaymentHistory")
                        .HasColumnType("jsonb");

                    b.Property<string>("Note")
                        .HasMaxLength(400)
                        .HasColumnType("character varying(400)");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentMethodName")
                        .HasColumnType("text");

                    b.Property<string>("PaymentStatusCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("PaymentStatusColor")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("PaymentStatusName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("SellerAddress")
                        .HasColumnType("text");

                    b.Property<string>("SellerBankAccount")
                        .HasColumnType("text");

                    b.Property<string>("SellerBankName")
                        .HasColumnType("text");

                    b.Property<string>("SellerName")
                        .HasColumnType("text");

                    b.Property<string>("SellerPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("SellerTaxCode")
                        .HasColumnType("text");

                    b.Property<decimal>("StillInDebtAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("TotalAmountInWords")
                        .HasColumnType("text");

                    b.Property<decimal>("TotalBeforeVatAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalVatAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_EInvoice");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoiceItems", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VatAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VatPercent")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("sm_EInvoiceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("sm_EInvoiceId");

                    b.ToTable("sm_EInvoiceItems");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoiceVatAnalytics", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("BeforeVatAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("EInvoiceId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Synthetic")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalPaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VatAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("sm_EInvoiceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("sm_EInvoiceId");

                    b.ToTable("sm_EInvoiceVatAnalytics");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Feedback.sm_Feedback", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string[]>("Module")
                        .HasColumnType("text[]");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<int>("Rate")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Feedback");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("EntityCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EntityTypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("EntityTypeName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("InventoryCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("InventoryName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaterialRequestCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("MaterialRequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("OriginalDocumentCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("OriginalDocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalDocumentType")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("StatusCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("StatusName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalQuantity")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("TransactionTypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("TransactionTypeName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("TypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_InventoryNote");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNoteItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("InventoryNoteId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Note")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("ProductCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.HasKey("Id");

                    b.HasIndex("InventoryNoteId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_InventoryNoteItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_IssueManagement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<List<jsonb_IssueActivityLogHistory>>("ActivityLogs")
                        .HasColumnType("jsonb");

                    b.Property<List<jsonb_Attachment>>("Attachments")
                        .HasColumnType("jsonb");

                    b.Property<List<jsonb_Attachment>>("AttachmentsResolve")
                        .HasColumnType("jsonb");

                    b.Property<string>("Code")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<string>("ContentResolve")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PriorityLevel")
                        .HasColumnType("text");

                    b.Property<string>("ReasonCancel")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_IssueManagement");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.KiemKho.mk_KiemKho", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CheckInventoryCode")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<string>>("ListWare")
                        .HasColumnType("text[]");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Tags")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ToDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("TypeCode")
                        .HasColumnType("text");

                    b.Property<string>("TypeName")
                        .HasColumnType("text");

                    b.Property<string>("WareCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_KiemKho");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NguyenVatLieu.mk_NguyenVatLieu", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("DonGia")
                        .HasColumnType("numeric");

                    b.Property<string>("DonViTinh")
                        .HasColumnType("text");

                    b.Property<Guid>("IdBom")
                        .HasColumnType("uuid");

                    b.Property<Guid>("IdVatTu")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaNguyenVatLieu")
                        .HasColumnType("text");

                    b.Property<int?>("SoLuong")
                        .HasColumnType("integer");

                    b.Property<string>("TenNguyenVatLieu")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TongTien")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("IdBom");

                    b.HasIndex("IdVatTu");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_NguyenVatLieu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhaCungCap.sm_Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("integer");

                    b.Property<string>("DistrictName")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<decimal>("ExpenseAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Fax")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_AccountBanking>>("ListAccountBanking")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NguoiPhuTrach")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal>("OrderCount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("integer");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("text");

                    b.Property<string>("SupplierGroupCode")
                        .HasColumnType("text");

                    b.Property<string>("TaxCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalDebtAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("WardCode")
                        .HasColumnType("integer");

                    b.Property<string>("WardName")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Supplier");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhomVatTu.mk_NhomVatTu", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaNhom")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenNhom")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_NhomVatTu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.PhongBan.mk_PhongBan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaPhongBan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenPhongBan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_PhongBan");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Quotation.sm_Quotation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CustomerAddress")
                        .HasColumnType("text");

                    b.Property<string>("CustomerCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("CustomerName")
                        .HasColumnType("text");

                    b.Property<string>("CustomerPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("CustomerTaxCode")
                        .HasColumnType("text");

                    b.Property<string>("DeliveryAddress")
                        .HasColumnType("text");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("DiscountReason")
                        .HasColumnType("text");

                    b.Property<string>("DiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OrderCode")
                        .HasColumnType("text");

                    b.Property<decimal>("OtherCostAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentMethodCode")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethodName")
                        .HasColumnType("text");

                    b.Property<string>("ProjectCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text");

                    b.Property<decimal>("ShippingCostAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<decimal>("SubTotalAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalVatAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("TypeCode")
                        .HasColumnType("text");

                    b.Property<decimal>("VatPercent")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Quotation");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Quotation.sm_QuotationItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AfterLineDiscountGoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Code")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("GoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsProductVATApplied")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("LineNote")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<decimal>("LineVATAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("LineVATCode")
                        .HasColumnType("text");

                    b.Property<decimal>("LineVATPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal>("LineVATableAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<decimal>("OrderDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("QuotationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Specifications")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("UnitPriceDiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("UnitPriceDiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.HasKey("Id");

                    b.HasIndex("QuotationId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_QuotationItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.StockTransaction.sm_Stock_Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<Guid?>("CheckInventoryId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("ClosingInventoryAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ClosingInventoryQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<decimal?>("ExportInventoryAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ExportInventoryQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("InitialStockQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("InventoryDecreased")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("InventoryIncreased")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal?>("OpeningInventoryAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OpeningInventoryQuantity")
                        .HasColumnType("numeric");

                    b.Property<string>("OriginalDocumentCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("OriginalDocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalDocumentType")
                        .HasColumnType("text");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasColumnType("text");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<decimal?>("ReceiptInventoryAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ReceiptInventoryQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("SellableQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StockTransactionQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TypeCode")
                        .HasColumnType("text");

                    b.Property<string>("TypeName")
                        .HasColumnType("text");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<string>("WareCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CheckInventoryId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Stock_Transaction");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.VatTu.sm_Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("ActualQuantity")
                        .HasColumnType("numeric");

                    b.Property<List<jsonb_Attachment>>("Attachments")
                        .HasColumnType("jsonb");

                    b.Property<decimal>("BalanceQuantity")
                        .HasColumnType("numeric");

                    b.Property<string>("Barcode")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("ExportVATCode")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<decimal>("ExportVATPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("ImportVATCode")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<decimal>("ImportVATPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal>("InitialStockQuantity")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOrder")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVATApplied")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_WareCodes>>("ListWareCodes")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal>("PlanQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("ProductGroupId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PurchaseUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("SellableQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("SellingUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProductGroupId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Product");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.VehicleRequest.sm_VehicleRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("DepartmentName")
                        .HasColumnType("text");

                    b.Property<string>("DepartureLocation")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("DestinationLocation")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("EndDateTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("NumPassengers")
                        .HasColumnType("integer");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("RejectNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("RequestCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("RequestedVehicleId")
                        .HasColumnType("uuid");

                    b.Property<string>("RequestedVehiclePlateNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RequestedVehicleId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_VehicleRequest");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.WorkingDay.mk_WorkingDay", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Day")
                        .HasColumnType("integer");

                    b.Property<bool>("IsOverride")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OriginalType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_WorkingDay");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.XuatNhapTon.mk_XuatNhapTon", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("DonGia")
                        .HasColumnType("numeric");

                    b.Property<string>("DonViTinh")
                        .HasColumnType("text");

                    b.Property<Guid?>("IdKiemKho")
                        .HasColumnType("uuid");

                    b.Property<Guid>("IdVatTu")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LoaiXuatNhapTon")
                        .HasColumnType("text");

                    b.Property<string>("MaKho")
                        .HasColumnType("text");

                    b.Property<string>("MaVatTu")
                        .HasColumnType("text");

                    b.Property<int?>("SoLuong")
                        .HasColumnType("integer");

                    b.Property<int?>("SoLuongChenhLech")
                        .HasColumnType("integer");

                    b.Property<int?>("SoLuongKiemKe")
                        .HasColumnType("integer");

                    b.Property<string>("TenVatTu")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TongTien")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IdKiemKho");

                    b.HasIndex("IdVatTu");

                    b.HasIndex("TenantId");

                    b.ToTable("mk_XuatNhapTon");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_CommentItems", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CommentId")
                        .HasColumnType("uuid");

                    b.Property<List<jsonb_Attachment>>("CommentItemsAttachments")
                        .HasColumnType("jsonb");

                    b.Property<string>("ContentItems")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PostStatusCodeInCommentItems")
                        .HasColumnType("text");

                    b.Property<List<string>>("TagUserIdsInCommentItems")
                        .HasColumnType("text[]");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CommentId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_CommentItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Comments", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<List<jsonb_Attachment>>("CommentAttachments")
                        .HasColumnType("jsonb");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("PostId")
                        .HasColumnType("uuid");

                    b.Property<string>("PostStatusCodeInComment")
                        .HasColumnType("text");

                    b.Property<List<string>>("TagUserIdsInComment")
                        .HasColumnType("text[]");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("PostId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Comments");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Construction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("CompletionByCompany")
                        .HasColumnType("text");

                    b.Property<string>("CompletionByInvestor")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionTemplateId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DocumentStatusCode")
                        .HasColumnType("text");

                    b.Property<string>("DocumentStatusName")
                        .HasColumnType("text");

                    b.Property<string>("ExecutionStatusCode")
                        .HasColumnType("text");

                    b.Property<string>("ExecutionStatusName")
                        .HasColumnType("text");

                    b.Property<Guid>("InvestorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OwnerTypeCode")
                        .HasColumnType("text");

                    b.Property<string>("PriorityCode")
                        .HasColumnType("text");

                    b.Property<string>("PriorityName")
                        .HasColumnType("text");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("StatusName")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("VoltageTypeCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionTemplateId");

                    b.HasIndex("InvestorId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionActivityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text");

                    b.Property<string>("CodeLinkDescription")
                        .HasColumnType("text");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ConstructionActivityLog");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionItems", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("GapQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("LineNote")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<decimal>("PlannedDefaultQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("PlannedQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("RealQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ConstructionItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionWeekReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_Attachment>>("FileAttachments")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LastWeekPlan")
                        .HasColumnType("text");

                    b.Property<string>("NextWeekPlan")
                        .HasColumnType("text");

                    b.Property<string>("ProcessResult")
                        .HasColumnType("text");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("StatusName")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ConstructionWeekReport");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_CustomerServiceComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<string>>("TagIds")
                        .HasColumnType("text[]");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_CustomerServiceComment");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ExecutionTeams", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("EmployeeAvatarUrl")
                        .HasColumnType("text");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uuid");

                    b.Property<string>("EmployeeName")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaPhongBan")
                        .HasColumnType("text");

                    b.Property<string>("MaTo")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ExecutionTeams");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InventoryCheckNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BalancedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CheckDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OrderCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("Tag")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("WareCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("WareName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_InventoryCheckNote");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InventoryCheckNoteItems", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("ActualQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CheckInventoryNoteId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("DifferenceQuantity")
                        .HasColumnType("numeric");

                    b.Property<string>("DifferenceType")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("NoteInventory")
                        .HasColumnType("text");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasColumnType("text");

                    b.Property<string>("ReasonInventory")
                        .HasColumnType("text");

                    b.Property<decimal>("RecordedQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CheckInventoryNoteId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_InventoryCheckNoteItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Investor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("InvestorTypeId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InvestorTypeId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Investor");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InvestorType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_InvestorType");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_IssueActivityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text");

                    b.Property<string>("CodeLinkDescription")
                        .HasColumnType("text");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_IssueActivityLog");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_MaterialRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("DateProcess")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_HistoryProcess>>("ListHistoryProcess")
                        .HasColumnType("jsonb");

                    b.Property<List<jsonb_MaterialRequest>>("MaterialRequestItem")
                        .HasColumnType("jsonb");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("Priority")
                        .HasColumnType("text");

                    b.Property<string>("PriorityName")
                        .HasColumnType("text");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("StatusName")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_MaterialRequest");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_MaterialRequestItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("IsApprove")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("LineNote")
                        .HasColumnType("text");

                    b.Property<Guid>("MaterialRequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<decimal>("PlannedQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("RequestQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("MaterialRequestId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_MaterialRequestItem");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ProductInventory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SellableQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("WarehouseCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ProductInventory");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_PurchaseOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ActionMadeByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("ActionMadeByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ActionMadeOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CancelledOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CancelledReason")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("DiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ImportStatusCode")
                        .HasColumnType("text");

                    b.Property<bool>("IsReturned")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_OtherCost>>("ListOtherCost")
                        .HasColumnType("jsonb");

                    b.Property<List<jsonb_HalfPayment>>("ListPayment")
                        .HasColumnType("jsonb");

                    b.Property<string>("LyDoTuChoi")
                        .HasColumnType("text");

                    b.Property<string>("MaterialRequestCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("MaterialRequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OrderCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<decimal?>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentStatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("PurchaseReason")
                        .HasColumnType("text");

                    b.Property<string>("Reference")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<decimal?>("RemainingAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<decimal?>("SubTotal")
                        .HasColumnType("numeric");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Total")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TotalDiscountsAccounts")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TotalOtherCost")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TotalTaxAccount")
                        .HasColumnType("numeric");

                    b.Property<string>("TypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("TypeName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<decimal?>("VATAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("WareCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_PurchaseOrder");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_PurchaseOrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AfterLineDiscountGoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("DiscountedUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("GoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsProductVATApplied")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReturnedItem")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal>("OrderDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("ProductCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("RemainingQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceDiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("UnitPriceDiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal>("VATAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("VATCode")
                        .HasColumnType("text");

                    b.Property<decimal>("VATPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VATableAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_PurchaseOrderItem");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Return_Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CancelledOnDate")
                        .HasColumnType("text");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("EntityCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("EntityTypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("EntityTypeName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_HalfPayment>>("ListPayment")
                        .HasColumnType("jsonb");

                    b.Property<string>("Note")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("OrderCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("OriginalDocumentCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("OriginalDocumentId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("ReasonCode")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("RefundStatusCode")
                        .HasColumnType("text");

                    b.Property<decimal>("RefundSubTotal")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("RemainingRefundAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("WareCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Return_Order");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Return_Order_Item", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("InitialQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("InitialUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasColumnType("text");

                    b.Property<decimal>("RemainingQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ReturnOrderId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ReturnedQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ReturnedUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ReturnOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Return_Order_Item");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SalesOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CancelledOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CancelledReason")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeliveryAddress")
                        .HasColumnType("text");

                    b.Property<decimal>("DeliveryFee")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("DiscountCode")
                        .HasColumnType("text");

                    b.Property<decimal>("DiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("DiscountReason")
                        .HasColumnType("text");

                    b.Property<string>("DiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ExportStatusCode")
                        .HasColumnType("text");

                    b.Property<string>("InvoiceReceiptAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsReturned")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_HalfPayment>>("ListPayment")
                        .HasColumnType("jsonb");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("OrderCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<decimal>("OtherCostAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentMethodCode")
                        .HasColumnType("text");

                    b.Property<string>("PaymentStatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("QuotationCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("QuotationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reference")
                        .HasColumnType("text");

                    b.Property<decimal?>("RemainingAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<decimal?>("SubTotal")
                        .HasColumnType("numeric");

                    b.Property<string>("Tags")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Total")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalQuantity")
                        .HasColumnType("numeric");

                    b.Property<string>("TypeCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("TypeName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<decimal>("VATAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("WareCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("WareName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_SalesOrder");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SalesOrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AfterLineDiscountGoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("GoodsAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsProductVATApplied")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReturnedItem")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("Note")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal>("OrderDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("ProductCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("QuotationUnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("RemainingQuantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceDiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("UnitPriceDiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("UnitPriceDiscountType")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal>("VATAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("VATCode")
                        .HasColumnType("text");

                    b.Property<decimal>("VATPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal>("VATableAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_SalesOrderItem");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SocialMediaPost", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ContentPostBody")
                        .HasColumnType("text");

                    b.Property<string>("ContentPostTitle")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<jsonb_Attachment>>("PostAttachments")
                        .HasColumnType("jsonb");

                    b.Property<string>("PostStatusCode")
                        .HasColumnType("text");

                    b.Property<List<string>>("TagUserIds")
                        .HasColumnType("text[]");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_SocialMediaPost");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_WarehouseTransferNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ExportWarehouseCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ExportWarehouseName")
                        .HasColumnType("text");

                    b.Property<string>("ImportWarehouseCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ImportWarehouseName")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransferNoteCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("TransferredByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransferredOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_WarehouseTransferNote");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_WarehouseTransferNoteItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("LineNo")
                        .HasColumnType("integer");

                    b.Property<string>("LineNote")
                        .HasColumnType("text");

                    b.Property<string>("ProductCode")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasColumnType("text");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TransferNoteID")
                        .HasColumnType("uuid");

                    b.Property<string>("Unit")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TransferNoteID");

                    b.ToTable("sm_WarehouseTransferNoteItem");
                });

            modelBuilder.Entity("NSPC.Data.erp_Attachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CopyRight")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DeletedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DocType")
                        .HasColumnType("text");

                    b.Property<string>("DocTypeName")
                        .HasColumnType("text");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .HasColumnType("text");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPrivate")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("License")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalFileName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("StatusCode")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UpdateFrequency")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("erp_Attachment");
                });

            modelBuilder.Entity("NSPC.Data.Idm_Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActiveDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<List<jsonb_Attachment>>("Attachments")
                        .HasColumnType("jsonb");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("EmailVerifyToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MST")
                        .HasColumnType("text");

                    b.Property<int>("MaxUsers")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Plan")
                        .HasColumnType("text");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Idm_Tenants");
                });

            modelBuilder.Entity("NSPC.Data.idm_User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActiveDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("AvatarUrl")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("BankAccountNo")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("BankUsername")
                        .HasColumnType("text");

                    b.Property<DateTime?>("Birthdate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("DeviceToken")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EmailVerifyToken")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<string>("FacebookUserId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<string>("GoogleUserId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("IdChucVu")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLockedOut")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastActivityDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("Ma")
                        .HasColumnType("text");

                    b.Property<string>("MaPhongBan")
                        .HasColumnType("text");

                    b.Property<string>("MaTo")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("Password")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("PasswordSalt")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("PlainTextPwd")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("ResetPasswordToken")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<List<string>>("RoleListCode")
                        .HasColumnType("text[]");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ThemeConfigs")
                        .HasColumnType("text");

                    b.Property<string>("UpdateLog")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.HasKey("Id");

                    b.HasIndex("IdChucVu");

                    b.HasIndex("TenantId");

                    b.ToTable("idm_User");
                });

            modelBuilder.Entity("NSPC.Data.IdmRight", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("GroupCode")
                        .HasColumnType("text");

                    b.Property<string>("GroupName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("IdmRight");
                });

            modelBuilder.Entity("NSPC.Data.IdmRightMapRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("RightCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("RightId")
                        .HasColumnType("uuid");

                    b.Property<string>("RoleCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RightId");

                    b.HasIndex("RoleId");

                    b.HasIndex("TenantId");

                    b.ToTable("IdmRightMapRole");
                });

            modelBuilder.Entity("NSPC.Data.IdmRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("idm_Role");
                });

            modelBuilder.Entity("NSPC.Data.sm_CodeType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("IconClass")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<int>("TranslationCount")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_CodeType");
                });

            modelBuilder.Entity("NSPC.Data.sm_CodeType_Translation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CodeTypeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CodeTypeId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_CodeType_Translation");
                });

            modelBuilder.Entity("NSPC.Data.sm_Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<DateTime?>("Birthdate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CustomerGroupCode")
                        .HasColumnType("text");

                    b.Property<List<string>>("CustomerSource")
                        .HasColumnType("text[]");

                    b.Property<string>("CustomerType")
                        .HasColumnType("text");

                    b.Property<decimal>("DebtAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("integer");

                    b.Property<string>("DistrictName")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<decimal>("ExpenseAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Fax")
                        .HasColumnType("text");

                    b.Property<string>("InformationToCopy")
                        .HasColumnType("text");

                    b.Property<string>("InitialRequirement")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastCareOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LinkFacebook")
                        .HasColumnType("text");

                    b.Property<string>("LinkTelegram")
                        .HasColumnType("text");

                    b.Property<string>("LinkTiktok")
                        .HasColumnType("text");

                    b.Property<List<string>>("ListPersonInCharge")
                        .HasColumnType("text[]");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<decimal>("OrderCount")
                        .HasColumnType("numeric");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("integer");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("text");

                    b.Property<string>("Sex")
                        .HasColumnType("text");

                    b.Property<string>("TaxCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalCareTimes")
                        .HasColumnType("integer");

                    b.Property<int>("TotalQuotationCount")
                        .HasColumnType("integer");

                    b.Property<int>("WardCode")
                        .HasColumnType("integer");

                    b.Property<string>("WardName")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Customer");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Subscribe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Status")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("SubscribeDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalEmailSentCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UnsubscribeDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Email_Subscribe");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Template", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Email_Template");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Template_Translation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BodyTemplate")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("EmailTemplateId")
                        .HasColumnType("uuid");

                    b.Property<string>("Language")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TitleTemplate")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.HasKey("Id");

                    b.HasIndex("EmailTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Email_Template_Translations");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Verification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ValidDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("Verified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("VerifiedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("VerifyToken")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Email_Verification");
                });

            modelBuilder.Entity("NSPC.Data.sm_Kho", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Binh")
                        .HasColumnType("numeric");

                    b.Property<int?>("CommuneCode")
                        .HasColumnType("integer");

                    b.Property<string>("CommuneName")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("DiaChi")
                        .HasColumnType("text");

                    b.Property<int?>("DistrictCode")
                        .HasColumnType("integer");

                    b.Property<string>("DistrictName")
                        .HasColumnType("text");

                    b.Property<decimal?>("GasDu")
                        .HasColumnType("numeric");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<bool>("IsCuaHang")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInitialized")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<string>("LoaiKho")
                        .HasColumnType("text");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Ma")
                        .HasColumnType("text");

                    b.Property<int?>("Order")
                        .HasColumnType("integer");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("integer");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("text");

                    b.Property<string>("Ten")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("VoBinh")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Kho");
                });

            modelBuilder.Entity("NSPC.Data.sm_LaiXe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<string>("Cccd")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Gplx")
                        .HasColumnType("text");

                    b.Property<Guid?>("IdPhuongTien")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MaTaiXe")
                        .HasColumnType("text");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("TenTaiXe")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IdPhuongTien")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_LaiXe");
                });

            modelBuilder.Entity("NSPC.Data.sm_LichSuChamSoc", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("CustomerServiceContent")
                        .HasColumnType("text");

                    b.Property<int>("DanhGia")
                        .HasColumnType("integer");

                    b.Property<DateTime?[]>("DateRange")
                        .HasColumnType("timestamp without time zone[]");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<string>>("Participants")
                        .HasColumnType("text[]");

                    b.Property<string>("Priority")
                        .HasColumnType("text");

                    b.Property<string>("PriorityColor")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("StatusCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_LichSuChamSoc");
                });

            modelBuilder.Entity("NSPC.Data.sm_LoaiXe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("MoTa")
                        .HasColumnType("text");

                    b.Property<string>("TenLoaiXe")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_LoaiXe");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("IsReceiverRead")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReceiverSeen")
                        .HasColumnType("boolean");

                    b.Property<string>("JsonData")
                        .HasColumnType("text");

                    b.Property<string>("JsonDataType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("ReceiverReadOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("ReceiverUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("ReceiverUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Notification");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification_Template", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Notification_Template");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification_Template_Translation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BodyHtmlTemplate")
                        .HasColumnType("text");

                    b.Property<string>("BodyPlainTextTemplate")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Language")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("NotificationTemplateId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TitleTemplate")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.HasKey("Id");

                    b.HasIndex("NotificationTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_Notification_Template_Translation");
                });

            modelBuilder.Entity("NSPC.Data.sm_PhuongTien", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<string>("BienSoXe")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("HangSanXuat")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("LoaiXeId")
                        .HasColumnType("uuid");

                    b.Property<string>("Model")
                        .HasColumnType("text");

                    b.Property<string>("NamSanXuat")
                        .HasColumnType("text");

                    b.Property<string>("SoKhung")
                        .HasColumnType("text");

                    b.Property<string>("SoMay")
                        .HasColumnType("text");

                    b.Property<string>("TaiTrong")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("WarehouseId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("LoaiXeId");

                    b.HasIndex("TenantId");

                    b.HasIndex("WarehouseId")
                        .IsUnique();

                    b.ToTable("sm_PhuongTien");
                });

            modelBuilder.Entity("NSPC.Data.sm_ProjectTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_ProjectTemplate");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<List<jsonb_Attachment>>("Attachments")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("ConstructionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConstructionId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_TaskManagement");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementAssignee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("TaskManagementId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TaskManagementId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("sm_TaskManagementAssignee");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("TaskManagementCommentReplyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TaskManagementId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("TaskManagementId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_TaskManagementComment");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("TaskManagementId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TaskManagementId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_TaskManagementHistory");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementMileStone", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("TaskManagementId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TaskManagementId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_TaskManagementMileStone");
                });

            modelBuilder.Entity("NSPC.Data.sm_TemplateStage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastModifiedByUserName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedOnDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectTemplateId")
                        .HasColumnType("uuid");

                    b.Property<int>("StepOrder")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProjectTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("sm_TemplateStage");
                });

            modelBuilder.Entity("FileManagement.Data.fm_Search_Sample", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.BsdNavigation", b =>
                {
                    b.HasOne("NSPC.Data.BsdNavigation", "Parent")
                        .WithMany("InverseParent")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("NSPC.Data.BsdNavigationMapRole", b =>
                {
                    b.HasOne("NSPC.Data.BsdNavigation", "Navigation")
                        .WithMany("NavigationRole")
                        .HasForeignKey("NavigationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Navigation");
                });

            modelBuilder.Entity("NSPC.Data.BsdParameter", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.cata_Commune", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.cata_District", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.cata_Province", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ActivityHistory.sm_ActiviyHisroty", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequest", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequestItems", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequest", null)
                        .WithMany("AdvanceRequestItems")
                        .HasForeignKey("sm_AdvanceRequestId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_Asset", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", "AssetLocation")
                        .WithMany()
                        .HasForeignKey("AssetLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.AssetCategories.sm_AssetType", "AssetType")
                        .WithMany()
                        .HasForeignKey("AssetTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.AssetCategories.sm_MeasureUnit", "MeasureUnit")
                        .WithMany()
                        .HasForeignKey("MeasureUnitId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("AssetLocation");

                    b.Navigation("AssetType");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("MeasureUnit");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_AssetLiquidationSheet", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Asset.sm_Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "Liquidator")
                        .WithMany()
                        .HasForeignKey("LiquidatorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Asset");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("Liquidator");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_AssetMaintenanceSheet", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Asset.sm_Asset", "Asset")
                        .WithMany("AssetMaintenanceSheets")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "Performer")
                        .WithMany()
                        .HasForeignKey("PerformerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Asset");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("Performer");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetAllocation.sm_AssetAllocation", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Asset.sm_Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", "FromLocation")
                        .WithMany()
                        .HasForeignKey("FromLocationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "FromUser")
                        .WithMany()
                        .HasForeignKey("FromUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", "ToLocation")
                        .WithMany()
                        .HasForeignKey("ToLocationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "ToUser")
                        .WithMany()
                        .HasForeignKey("ToUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Asset");

                    b.Navigation("CreatedByUser");

                    b.Navigation("FromLocation");

                    b.Navigation("FromUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("ToLocation");

                    b.Navigation("ToUser");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_AssetGroup", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_AssetType", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.AssetCategories.sm_AssetGroup", "AssetGroup")
                        .WithMany("AssetTypes")
                        .HasForeignKey("AssetGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("AssetGroup");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_MeasureUnit", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetHistory.sm_AssetUsageHistory", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Asset.sm_Asset", "Asset")
                        .WithMany("AssetUsageHistories")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Asset");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("Location");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetIncident.sm_AssetIncident", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Asset.sm_Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Asset");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangLuongItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.BangTinhLuong.mk_CacKhoanTroCap", "CacKhoanTroCap")
                        .WithMany()
                        .HasForeignKey("CacKhoanTroCapId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangTinhLuong", "mk_BangTinhLuong")
                        .WithMany("BangLuongItem")
                        .HasForeignKey("IdBangTinhLuong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CacKhoanTroCap");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_BangTinhLuong");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangTinhLuong", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_CacKhoanTroCap", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BHXH.mk_BHXH", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.mk_Bom", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("IdSanPham")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.sm_Bom", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.sm_Materials", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Bom.sm_Bom", "sm_Bom")
                        .WithMany("Materials")
                        .HasForeignKey("BomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Bom");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CashbookTransaction.sm_Cashbook_Transaction", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequest", "sm_AdvanceRequest")
                        .WithMany()
                        .HasForeignKey("AdvanceRequestId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.Contract.sm_Contract", "sm_Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "Mk_DuAn")
                        .WithMany("sm_Cashbook_Transaction")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Mk_DuAn");

                    b.Navigation("sm_AdvanceRequest");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Contract");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CauHinhNhanSu.mk_CauHinhNhanSu", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "idm_User")
                        .WithMany()
                        .HasForeignKey("IdUser")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("idm_User");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCong", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCongItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.ChamCong.mk_ChamCong", "mk_ChamCong")
                        .WithMany("ListChamCong")
                        .HasForeignKey("IdChamCong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_ChamCong");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.NgayTrongThang", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.ChamCong.mk_ChamCongItem", null)
                        .WithMany("NgayTrongThang")
                        .HasForeignKey("mk_ChamCongItemId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChucVu.mk_ChucVu", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.CodeType.sm_CodeType_Item", b =>
                {
                    b.HasOne("NSPC.Data.sm_CodeType", "sm_CodeType")
                        .WithMany("CodeTypeItems")
                        .HasForeignKey("CodeTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_CodeType");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Contract.sm_Contract", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.sm_CodeType", "ConsultingService")
                        .WithMany()
                        .HasForeignKey("ConsultingServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.sm_TemplateStage", "TemplateStage")
                        .WithMany()
                        .HasForeignKey("TemplateStageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Construction");

                    b.Navigation("ConsultingService");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("TemplateStage");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.DebtTransaction.sm_DebtTransaction", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.DuAn.mk_DuAn", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoice", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoiceItems", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.EInvoice.sm_EInvoice", null)
                        .WithMany("EInvoiceItems")
                        .HasForeignKey("sm_EInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoiceVatAnalytics", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.EInvoice.sm_EInvoice", null)
                        .WithMany("EInvoiceVatAnalytics")
                        .HasForeignKey("sm_EInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Feedback.sm_Feedback", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNote", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.Contract.sm_Contract", "sm_Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany("sm_InventoryNote")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Contract");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNoteItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNote", "sm_InventoryNote")
                        .WithMany("InventoryNoteItems")
                        .HasForeignKey("InventoryNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_InventoryNote");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_IssueManagement", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "Idm_User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Idm_User");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.KiemKho.mk_KiemKho", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NguyenVatLieu.mk_NguyenVatLieu", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Bom.mk_Bom", "mk_Bom")
                        .WithMany("NguyenVatLieu")
                        .HasForeignKey("IdBom")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany("mk_NguyenVatLieu")
                        .HasForeignKey("IdVatTu")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("mk_Bom");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhaCungCap.sm_Supplier", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhomVatTu.mk_NhomVatTu", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.PhongBan.mk_PhongBan", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Quotation.sm_Quotation", b =>
                {
                    b.HasOne("NSPC.Data.sm_Customer", "sm_Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany("sm_Quotation")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Customer");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Quotation.sm_QuotationItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.Quotation.sm_Quotation", "sm_Quotation")
                        .WithMany("QuotationItem")
                        .HasForeignKey("QuotationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Quotation");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.StockTransaction.sm_Stock_Transaction", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.KiemKho.mk_KiemKho", "mk_KiemKho")
                        .WithMany("Sm_Stock_Transactions")
                        .HasForeignKey("CheckInventoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("mk_KiemKho");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.VatTu.sm_Product", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.NhomVatTu.mk_NhomVatTu", "mk_NhomVatTu")
                        .WithMany("Sm_Products")
                        .HasForeignKey("ProductGroupId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_NhomVatTu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.VehicleRequest.sm_VehicleRequest", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.sm_CodeType", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Entity.sm_Construction", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.sm_PhuongTien", "RequestedVehicle")
                        .WithMany()
                        .HasForeignKey("RequestedVehicleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("Department");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LastModifiedByUser");

                    b.Navigation("Project");

                    b.Navigation("RequestedVehicle");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.WorkingDay.mk_WorkingDay", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.XuatNhapTon.mk_XuatNhapTon", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.KiemKho.mk_KiemKho", "mk_KiemKho")
                        .WithMany()
                        .HasForeignKey("IdKiemKho")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("IdVatTu")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("mk_KiemKho");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_CommentItems", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Comments", "sm_Comments")
                        .WithMany("sm_CommentItems")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Comments");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Comments", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_SocialMediaPost", "sm_SocialMediaPost")
                        .WithMany("sm_Comments")
                        .HasForeignKey("PostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_SocialMediaPost");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Construction", b =>
                {
                    b.HasOne("NSPC.Data.sm_ProjectTemplate", "sm_ProjectTemplate")
                        .WithMany()
                        .HasForeignKey("ConstructionTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_Investor", "sm_Investor")
                        .WithMany()
                        .HasForeignKey("InvestorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Investor");

                    b.Navigation("sm_ProjectTemplate");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionActivityLog", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany("sm_ConstructionActivityLog")
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionItems", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Product");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ConstructionWeekReport", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_CustomerServiceComment", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ExecutionTeams", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany("sm_ExecutionTeams")
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InventoryCheckNote", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InventoryCheckNoteItems", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_InventoryCheckNote", "sm_InventoryCheckNote")
                        .WithMany("Items")
                        .HasForeignKey("CheckInventoryNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("sm_InventoryCheckNote");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Investor", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_InvestorType", "InvestorType")
                        .WithMany("Investor")
                        .HasForeignKey("InvestorTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("InvestorType");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InvestorType", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_IssueActivityLog", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_MaterialRequest", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Construction");

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_MaterialRequestItem", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_MaterialRequest", "sm_MaterialRequest")
                        .WithMany("MaterialRequestItems")
                        .HasForeignKey("MaterialRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_MaterialRequest");

                    b.Navigation("sm_Product");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_ProductInventory", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_PurchaseOrder", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.Contract.sm_Contract", "sm_Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany("sm_PurchaseOrder")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.NhaCungCap.sm_Supplier", "sm_Supplier")
                        .WithMany("mk_QuanLyKho")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Contract");

                    b.Navigation("sm_Supplier");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_PurchaseOrderItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_PurchaseOrder", "sm_PurchaseOrder")
                        .WithMany("Items")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("sm_PurchaseOrder");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Return_Order", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.Contract.sm_Contract", "sm_Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Contract");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Return_Order_Item", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_Return_Order", "sm_Return_Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("ReturnOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("sm_Return_Order");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SalesOrder", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Data.Entity.Contract.sm_Contract", "sm_Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.sm_Customer", "sm_Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Construction");

                    b.Navigation("sm_Contract");

                    b.Navigation("sm_Customer");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SalesOrderItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Entity.sm_SalesOrder", "sm_SalesOrder")
                        .WithMany("SalesOrderItems")
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Product");

                    b.Navigation("sm_SalesOrder");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SocialMediaPost", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_WarehouseTransferNote", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_WarehouseTransferNoteItem", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.VatTu.sm_Product", "Sm_Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Entity.sm_WarehouseTransferNote", "sm_WarehouseTransferNote")
                        .WithMany("Items")
                        .HasForeignKey("TransferNoteID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Sm_Product");

                    b.Navigation("sm_WarehouseTransferNote");
                });

            modelBuilder.Entity("NSPC.Data.erp_Attachment", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.idm_User", b =>
                {
                    b.HasOne("NSPC.Data.Data.Entity.ChucVu.mk_ChucVu", "mk_ChucVu")
                        .WithMany()
                        .HasForeignKey("IdChucVu")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_ChucVu");
                });

            modelBuilder.Entity("NSPC.Data.IdmRight", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.IdmRightMapRole", b =>
                {
                    b.HasOne("NSPC.Data.IdmRight", "Right")
                        .WithMany("RightMapRole")
                        .HasForeignKey("RightId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.IdmRole", "Role")
                        .WithMany("RoleMapRight")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("Right");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("NSPC.Data.IdmRole", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_CodeType", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_CodeType_Translation", b =>
                {
                    b.HasOne("NSPC.Data.sm_CodeType", "ub_CodeType")
                        .WithMany("sm_CodeType_Translation")
                        .HasForeignKey("CodeTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("ub_CodeType");
                });

            modelBuilder.Entity("NSPC.Data.sm_Customer", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Subscribe", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Template", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Template_Translation", b =>
                {
                    b.HasOne("NSPC.Data.sm_Email_Template", "sm_Email_Template")
                        .WithMany("sm_Email_Template_Translation")
                        .HasForeignKey("EmailTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Email_Template");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Verification", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Kho", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.sm_Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Customer");

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_LaiXe", b =>
                {
                    b.HasOne("NSPC.Data.sm_PhuongTien", "PhuongTien")
                        .WithOne("TaiXe")
                        .HasForeignKey("NSPC.Data.sm_LaiXe", "IdPhuongTien")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("PhuongTien");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NSPC.Data.sm_LichSuChamSoc", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.sm_Customer", "sm_Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Data.Entity.DuAn.mk_DuAn", "mk_DuAn")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("mk_DuAn");

                    b.Navigation("sm_Customer");
                });

            modelBuilder.Entity("NSPC.Data.sm_LoaiXe", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "ReceiverUser")
                        .WithMany()
                        .HasForeignKey("ReceiverUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Idm_Tenant");

                    b.Navigation("ReceiverUser");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification_Template", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification_Template_Translation", b =>
                {
                    b.HasOne("NSPC.Data.sm_Notification_Template", "ub_Notification_Template")
                        .WithMany("Translations")
                        .HasForeignKey("NotificationTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("ub_Notification_Template");
                });

            modelBuilder.Entity("NSPC.Data.sm_PhuongTien", b =>
                {
                    b.HasOne("NSPC.Data.sm_LoaiXe", "LoaiXe")
                        .WithMany("listPhuongTien")
                        .HasForeignKey("LoaiXeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.sm_Kho", "Warehouse")
                        .WithOne("PhuongTien")
                        .HasForeignKey("NSPC.Data.sm_PhuongTien", "WarehouseId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("LoaiXe");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("NSPC.Data.sm_ProjectTemplate", b =>
                {
                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagement", b =>
                {
                    b.HasOne("NSPC.Data.Entity.sm_Construction", "sm_Construction")
                        .WithMany()
                        .HasForeignKey("ConstructionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_Construction");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementAssignee", b =>
                {
                    b.HasOne("NSPC.Data.sm_TaskManagement", "sm_TaskManagement")
                        .WithMany("sm_TaskManagementAssignees")
                        .HasForeignKey("TaskManagementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("NSPC.Data.idm_User", "idm_User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Idm_Tenant");

                    b.Navigation("idm_User");

                    b.Navigation("sm_TaskManagement");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementComment", b =>
                {
                    b.HasOne("NSPC.Data.idm_User", "idm_User")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.sm_TaskManagement", "sm_TaskManagement")
                        .WithMany("sm_TaskManagementComments")
                        .HasForeignKey("TaskManagementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("idm_User");

                    b.Navigation("sm_TaskManagement");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementHistory", b =>
                {
                    b.HasOne("NSPC.Data.sm_TaskManagement", "sm_TaskManagement")
                        .WithMany("sm_TaskManagementHistories")
                        .HasForeignKey("TaskManagementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_TaskManagement");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagementMileStone", b =>
                {
                    b.HasOne("NSPC.Data.sm_TaskManagement", "sm_TaskManagement")
                        .WithMany("sm_TaskManagementMileStones")
                        .HasForeignKey("TaskManagementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("sm_TaskManagement");
                });

            modelBuilder.Entity("NSPC.Data.sm_TemplateStage", b =>
                {
                    b.HasOne("NSPC.Data.sm_ProjectTemplate", "ProjectTemplate")
                        .WithMany("TemplateStages")
                        .HasForeignKey("ProjectTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NSPC.Data.Idm_Tenant", "Idm_Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Idm_Tenant");

                    b.Navigation("ProjectTemplate");
                });

            modelBuilder.Entity("NSPC.Data.BsdNavigation", b =>
                {
                    b.Navigation("InverseParent");

                    b.Navigation("NavigationRole");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AdvanceRequest.sm_AdvanceRequest", b =>
                {
                    b.Navigation("AdvanceRequestItems");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Asset.sm_Asset", b =>
                {
                    b.Navigation("AssetMaintenanceSheets");

                    b.Navigation("AssetUsageHistories");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetCategories.sm_AssetGroup", b =>
                {
                    b.Navigation("AssetTypes");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.AssetLocation.sm_AssetLocation", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.BangTinhLuong.mk_BangTinhLuong", b =>
                {
                    b.Navigation("BangLuongItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.mk_Bom", b =>
                {
                    b.Navigation("NguyenVatLieu");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Bom.sm_Bom", b =>
                {
                    b.Navigation("Materials");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCong", b =>
                {
                    b.Navigation("ListChamCong");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.ChamCong.mk_ChamCongItem", b =>
                {
                    b.Navigation("NgayTrongThang");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.DuAn.mk_DuAn", b =>
                {
                    b.Navigation("sm_Cashbook_Transaction");

                    b.Navigation("sm_InventoryNote");

                    b.Navigation("sm_PurchaseOrder");

                    b.Navigation("sm_Quotation");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.EInvoice.sm_EInvoice", b =>
                {
                    b.Navigation("EInvoiceItems");

                    b.Navigation("EInvoiceVatAnalytics");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.InventoryNote.sm_InventoryNote", b =>
                {
                    b.Navigation("InventoryNoteItems");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.KiemKho.mk_KiemKho", b =>
                {
                    b.Navigation("Sm_Stock_Transactions");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhaCungCap.sm_Supplier", b =>
                {
                    b.Navigation("mk_QuanLyKho");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.NhomVatTu.mk_NhomVatTu", b =>
                {
                    b.Navigation("Sm_Products");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.Quotation.sm_Quotation", b =>
                {
                    b.Navigation("QuotationItem");
                });

            modelBuilder.Entity("NSPC.Data.Data.Entity.VatTu.sm_Product", b =>
                {
                    b.Navigation("mk_NguyenVatLieu");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Comments", b =>
                {
                    b.Navigation("sm_CommentItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Construction", b =>
                {
                    b.Navigation("sm_ConstructionActivityLog");

                    b.Navigation("sm_ExecutionTeams");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InventoryCheckNote", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_InvestorType", b =>
                {
                    b.Navigation("Investor");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_MaterialRequest", b =>
                {
                    b.Navigation("MaterialRequestItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_PurchaseOrder", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_Return_Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SalesOrder", b =>
                {
                    b.Navigation("SalesOrderItems");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_SocialMediaPost", b =>
                {
                    b.Navigation("sm_Comments");
                });

            modelBuilder.Entity("NSPC.Data.Entity.sm_WarehouseTransferNote", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("NSPC.Data.IdmRight", b =>
                {
                    b.Navigation("RightMapRole");
                });

            modelBuilder.Entity("NSPC.Data.IdmRole", b =>
                {
                    b.Navigation("RoleMapRight");
                });

            modelBuilder.Entity("NSPC.Data.sm_CodeType", b =>
                {
                    b.Navigation("CodeTypeItems");

                    b.Navigation("sm_CodeType_Translation");
                });

            modelBuilder.Entity("NSPC.Data.sm_Email_Template", b =>
                {
                    b.Navigation("sm_Email_Template_Translation");
                });

            modelBuilder.Entity("NSPC.Data.sm_Kho", b =>
                {
                    b.Navigation("PhuongTien");
                });

            modelBuilder.Entity("NSPC.Data.sm_LoaiXe", b =>
                {
                    b.Navigation("listPhuongTien");
                });

            modelBuilder.Entity("NSPC.Data.sm_Notification_Template", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("NSPC.Data.sm_PhuongTien", b =>
                {
                    b.Navigation("TaiXe");
                });

            modelBuilder.Entity("NSPC.Data.sm_ProjectTemplate", b =>
                {
                    b.Navigation("TemplateStages");
                });

            modelBuilder.Entity("NSPC.Data.sm_TaskManagement", b =>
                {
                    b.Navigation("sm_TaskManagementAssignees");

                    b.Navigation("sm_TaskManagementComments");

                    b.Navigation("sm_TaskManagementHistories");

                    b.Navigation("sm_TaskManagementMileStones");
                });
#pragma warning restore 612, 618
        }
    }
}
