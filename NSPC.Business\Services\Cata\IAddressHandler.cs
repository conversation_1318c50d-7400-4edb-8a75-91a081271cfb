﻿using NSPC.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NSPC.Business
{
    public interface IAddressHandler
    {
        Task<Response> GetTinh(AddressQueryModel query);

        Task<Response> GetH<PERSON>en(AddressQueryModel query);

        Task<Response> Get<PERSON>huong(AddressQueryModel query);

        Task<Response> UpdateProvince(float provinceId, ProvinceUpdateModel model);
        Task<Response> UpdateDistrict(float districtId, DistrictUpdateModel model);
        Task<Response> UpdateProvinceMany(List<ProvinceUpdateModel> model);
        Task<Response> UpdateDistrictMany(List<DistrictUpdateModel> model);
        Task<string> GetNotifyMessage();
      //  Task AutoUpdateShippingStatus();
    }
}