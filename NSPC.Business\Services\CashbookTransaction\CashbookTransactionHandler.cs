﻿using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using MongoDB.Driver.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using NSPC.Business.Services.DebtTransaction;
using NSPC.Business.Services.Quotation;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using NSPC.Data.Data.Entity.CashbookTransaction;
using NSPC.Data.Data.Entity.JsonbEntity;
using Serilog;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using NSPC.Business.Services.ConstructionActitvityLog;
using static NSPC.Common.Helper;

namespace NSPC.Business.Services.CashbookTransaction
{
    public class CashbookTransactionHandler : ICashbookTransactionHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        private readonly IDebtTransactionHandler _debtTransactionHandler;
        private readonly IMemoryCache _cache;
        private readonly IConstructionActivityLogHandler _constructionActivityLogHandler;
        public CashbookTransactionHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper, IDebtTransactionHandler debtTransactionHandler, IMemoryCache cache, IConstructionActivityLogHandler constructionActivityLogHandler)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _debtTransactionHandler = debtTransactionHandler;
            _staticsFolder = Utils.GetConfig("StaticFiles:Folder");
            _cache = cache;
            _constructionActivityLogHandler = constructionActivityLogHandler;
        }
        public async Task<Response<CashbookTransactionViewModel>> Create(CashbookTransactionCreateUpdateModel model, RequestUser currentUser)
        {
            try
            {
                // Validate: Mã phiếu kiểm đã tồn tại
                var checkCode = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Code == model.Code);
                if (checkCode != null)
                    return Helper.CreateBadRequestResponse<CashbookTransactionViewModel>
                        (string.Format("Mã {0} đã tồn tại!", model.Code));

                if (model.Amount < 0)
                    return Helper.CreateBadRequestResponse<CashbookTransactionViewModel>(string.Format("Giá trị phải lớn hơn 0"));


                var entity = _mapper.Map<sm_Cashbook_Transaction>(model);
                entity.Id = Guid.NewGuid();

                if (model.Code == null)
                {
                    entity.Code = await _autoGeneratedCashbookCode(model.TransactionTypeCode);
                }
                if (model.PurposeCode == CashbookTransactionConstants.AUTO_RECEIPT_PURPOSE || model.PurposeCode == CashbookTransactionConstants.AUTO_PAYMENT_PURPOSE || model.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction)
                {
                    entity.IsActive = StatusCashbookTransaction.COMPLETED;
                }
                else
                {
                    entity.IsActive = StatusCashbookTransaction.WAIT_TRANSFER;
                }
                entity.CreatedByUserId = currentUser.UserId;
                entity.CreatedByUserName = currentUser.UserName;
                entity.CreatedOnDate = DateTime.Now;
                entity.PurposeName = CodeTypeCollection.Instance.FetchCode(entity.PurposeCode, "vn", currentUser.TenantId)?.Title;              

                if (entity.AdvanceRequestId.HasValue)
                {
                    var advanceRequest = await _dbContext.sm_AdvanceRequest.FirstOrDefaultAsync(x => x.Id == entity.AdvanceRequestId);
                    if (advanceRequest != null)
                    {
                        entity.OriginalDocumentId = advanceRequest.Id;
                        entity.OriginalDocumentCode = advanceRequest.Code;
                        entity.OriginalDocumentType = "ADVANCE_REQUEST";
                    }
                }

                _dbContext.sm_Cashbook_Transaction.Add(entity);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động duyệt yêu cầu vật tư vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = entity.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? "đã tạo phiếu thu" : "đã tạo phiếu chi",
                        CodeLinkDescription = $"{entity.Code}",
                        OrderId =  entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion
                }

                if (entity.IsDebt)
                {
                    await _debtTransactionHandler.CreateDebtCashbookTransaction(entity.Id, currentUser);
                }

                var result = await GetById(entity.Id);

                await _updateProjectSummary(entity);
                if (result.IsSuccess)
                {
                    result.Message = "Thêm mới thành công";
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<CashbookTransactionViewModel>(ex);
            }
        }



        /// <summary>
        /// Tạo nhiều phiếu thu/chi
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>

        public async Task<Response<CashbookTransactionViewModel>> CreateMultiple(CashbookTransactionCreateUpdateMutipleModel model, RequestUser currentUser)
        {
            try
            {
                var transactionsToAdd = new List<sm_Cashbook_Transaction>(); // List to hold all transactions
                var failedTransactions = new List<string>(); // To hold failed transaction codes

                foreach (var item in model.ListCashbookTransaction)
                {
                    // Check if the Code already exists in the database
                    var checkCode = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Code == item.Code);
                    if (checkCode != null)
                    {
                        failedTransactions.Add(item.Code);  // Add to failed list if code already exists
                        continue; // Skip the current transaction and move to the next one
                    }

                    // Validate that the Amount is greater than 0
                    if (item.Amount <= 0)
                    {
                        failedTransactions.Add(item.Code);  // Add to failed list if validation fails
                        continue;
                    }              

                    // Create new entity for each transaction
                    var entity = _mapper.Map<sm_Cashbook_Transaction>(item);
                    entity.Id = Guid.NewGuid();
                    entity.ConstructionId = model.ConstructionId;
                    entity.ContractId = model.ContractId;
                    entity.AdvanceRequestId = item.AdvanceRequestId;

                    if (entity.AdvanceRequestId.HasValue)
                    {
                        var advanceRequest = await _dbContext.sm_AdvanceRequest.FirstOrDefaultAsync(x => x.Id == entity.AdvanceRequestId);
                        if (advanceRequest != null)
                        {
                            entity.OriginalDocumentId = advanceRequest.Id;
                            entity.OriginalDocumentCode = advanceRequest.Code;
                            entity.OriginalDocumentType = "ADVANCE_REQUEST";
                        }
                    }

                    // Auto-generate Code if it's null
                    if (item.Code == null)
                    {
                        entity.Code = await _autoGeneratedCashbookCode(item.TransactionTypeCode);
                    }

                    // Set the transaction status based on PurposeCode
                    if (item.PurposeCode == CashbookTransactionConstants.AUTO_RECEIPT_PURPOSE || item.PurposeCode == CashbookTransactionConstants.AUTO_PAYMENT_PURPOSE)
                    {
                        entity.IsActive = StatusCashbookTransaction.COMPLETED;
                    }
                    else
                    {
                        entity.IsActive = StatusCashbookTransaction.WAIT_TRANSFER;
                    }

                    // Set metadata for the transaction
                    entity.CreatedByUserId = currentUser.UserId;
                    entity.TransactionTypeCode = item.TransactionTypeCode;
                    entity.ReceiptDate = item.ReceiptDate;
                    entity.PurposeCode = item.PurposeCode;
                    entity.Amount = item.Amount;
                    entity.CreatedByUserName = currentUser.UserName;
                    entity.CreatedOnDate = DateTime.Now;
                    entity.PurposeName = CodeTypeCollection.Instance.FetchCode(entity.PurposeCode, "vn", currentUser.TenantId)?.Title;

                    // Handle Advance Request if it exists
                    if (entity.AdvanceRequestId.HasValue)
                    {
                        var advanceRequest = await _dbContext.sm_AdvanceRequest.FirstOrDefaultAsync(x => x.Id == entity.AdvanceRequestId);
                        if (advanceRequest != null)
                        {
                            entity.OriginalDocumentId = advanceRequest.Id;
                            entity.OriginalDocumentCode = advanceRequest.Code;
                            entity.OriginalDocumentType = "ADVANCE_REQUEST";
                        }
                    }

                    transactionsToAdd.Add(entity); // Add entity to the list to be added in bulk

                    // If it's a debt, handle debt-related transactions
                    if (entity.IsDebt)
                    {
                        await _debtTransactionHandler.CreateDebtCashbookTransaction(entity.Id, currentUser);
                    }

                    await _updateProjectSummary(entity); // Handle project summary update for each transaction
                }

                // If no valid transactions to add, return a bad request response
                if (!transactionsToAdd.Any())
                {
                    return Helper.CreateBadRequestResponse<CashbookTransactionViewModel>("Không có phiếu thu hợp lệ để thêm.");
                }

                // Add all valid transactions to the database in one go
                _dbContext.sm_Cashbook_Transaction.AddRange(transactionsToAdd);

                // Save changes once for all the valid transactions
                await _dbContext.SaveChangesAsync();

                // Handle project summary update for the last transaction
                var result = await GetById(transactionsToAdd.Last().Id);

                // Modify success message if needed
                if (result.IsSuccess)
                {
                    result.Message = "Thêm mới thành công";
                }

                // If there are failed transactions, add them to the response message
                if (failedTransactions.Any())
                {
                    result.Message += $" Những mã phiếu thu sau không thể thêm do lỗi: {string.Join(", ", failedTransactions)}";
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<CashbookTransactionViewModel>(ex);
            }
        }

        public async Task<Response<Pagination<CashbookTransactionViewModel>>> GetPage(CashbookTransactionQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);

                var queryResult = _dbContext.sm_Cashbook_Transaction
                    .Include(x => x.Mk_DuAn)
                    .Include(x => x.sm_Contract)
                    .Include(x => x.sm_Construction)
                    .Include(x => x.sm_AdvanceRequest)
                    .AsNoTracking()
                    .Where(predicate);
                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<CashbookTransactionViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<Pagination<CashbookTransactionViewModel>>(ex);
            }
        }

        public async Task<Response<CashbookTransactionViewModel>> GetById(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_Cashbook_Transaction
                    .Include(x => x.Mk_DuAn)
                    .Include(x => x.sm_Contract)
                    .Include(x => x.sm_Construction)
                    .Include(x => x.sm_AdvanceRequest)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<CashbookTransactionViewModel>("Mã không tồn tại trong hệ thống.");

                var result = _mapper.Map<CashbookTransactionViewModel>(entity);


                return new Response<CashbookTransactionViewModel>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}", id);
                return Helper.CreateExceptionResponse<CashbookTransactionViewModel>(ex);
            }
        }

        /// <summary>
        /// Huỷ kích hoạt nhiều phiếu thu/chi
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<Response> DeactiveCashbookTransactionsAsync(List<Guid> ids)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
                // Review 180924: Rewrite đoạn dưới
                //entities = await _dbContext.sm_Cashbook_Transaction.Where(x => ids.contains(x.Id)).Tolist();

                // 1 Select entities
                // 2 foreach entities
                // 3 check & update

                foreach (var id in ids)
                {
                    var entity = await _dbContext.sm_Cashbook_Transaction.Where(x => x.Id == id).FirstOrDefaultAsync();
                    var entities = _dbContext.sm_Cashbook_Transaction.Where(x => ids.Contains(x.Id)).ToList();
                    if (entities == null | entities.Count == 0)
                        return Helper.CreateNotFoundResponse("Không tìm thấy phiếu");

                    if (entity.IsActive == StatusCashbookTransaction.CANCELED)
                        return Helper.CreateBadRequestResponse("Không được hủy các phiếu có trạng thái “Đã hủy”");

                    if (entity.PurposeCode == CashbookTransactionConstants.AUTO_RECEIPT_PURPOSE ||
                        entity.PurposeCode == CashbookTransactionConstants.AUTO_PAYMENT_PURPOSE)
                        return Helper.CreateBadRequestResponse("Không được hủy các phiếu “Tự động”");

                    entity.IsActive = StatusCashbookTransaction.CANCELED;

                    if (entity.ProjectId.HasValue)
                    {
                        await _updateProjectSummary(entity);
                    }

                }
                await _dbContext.SaveChangesAsync();

                // xử lý logics giống hàm Create,hàm Hủy thì phải trừ thay cho cộng
                // Tạo bản ghi giao dịch nợ tương ứng với phiếu thu/chi
                var latestDebtTransaction = await _dbContext.sm_DebtTransaction.Where(dt => ids.Contains(dt.OriginalDocumentId.Value))
                    .OrderByDescending(dt => dt.CreatedOnDate).AsNoTracking().FirstOrDefaultAsync();
                if (latestDebtTransaction != null)
                {
                    // Thiết lập ChangeAmount theo logic cụ thể
                    decimal changeAmount;
                    // Hủy phiếu thu khách hàng là số dương, hủy phiếu thu nhà cung cấp là số âm, hủy phiếu chi khách hàng là số âm, hủy phiếu chi nhà cung cấp là số dương
                    if (latestDebtTransaction.OriginalDocumentType == DebtTransactionOriginalDocumentTypesConstants.RECEIPT_VOUCHER)
                    {
                        changeAmount = latestDebtTransaction.EntityType == DebtTransactionEntityTypesConstants.CUSTOMER
                            ? Math.Abs(latestDebtTransaction.ChangeAmount)
                            : -Math.Abs(latestDebtTransaction.ChangeAmount);
                    }
                    else if (latestDebtTransaction.OriginalDocumentType == DebtTransactionOriginalDocumentTypesConstants.PAYMENT_VOUCHER)
                    {
                        changeAmount = latestDebtTransaction.EntityType == DebtTransactionEntityTypesConstants.CUSTOMER
                            ? -Math.Abs(latestDebtTransaction.ChangeAmount)
                            : Math.Abs(latestDebtTransaction.ChangeAmount);
                    }
                    else
                    {
                        throw new Exception("Loại phiếu không hợp lệ.");
                    }

                    var debtTransaction = new DebtTransactionCreateModel
                    {
                        EntityId = latestDebtTransaction.EntityId,
                        EntityCode = latestDebtTransaction.EntityCode,
                        EntityType = latestDebtTransaction.EntityType,
                        EntityName = latestDebtTransaction.EntityName,
                        OriginalDocumentId = latestDebtTransaction.OriginalDocumentId,
                        OriginalDocumentCode = latestDebtTransaction.OriginalDocumentCode,
                        OriginalDocumentType = latestDebtTransaction.OriginalDocumentType,
                        ChangeAmount = changeAmount,
                        DebtAmount = latestDebtTransaction.DebtAmount + changeAmount,
                        //Action check theo loại giao dịch phiếu thu hoặc phiếu chi
                        Action = latestDebtTransaction.OriginalDocumentType == DebtTransactionOriginalDocumentTypesConstants.RECEIPT_VOUCHER
                            ? DebtTransactionActionsCodesConstants.RECEIPT_VOUCHER_CANCEL
                            : DebtTransactionActionsCodesConstants.PAYMENT_VOUCHER_CANCEL,
                        // Note check theo loại giao dịch phiếu thu hoặc phiếu chi
                        Note = latestDebtTransaction.OriginalDocumentType == DebtTransactionOriginalDocumentTypesConstants.RECEIPT_VOUCHER
                            ? DebtTransactionNotesConstants.RECEIPT_VOUCHER_CANCEL
                            : DebtTransactionNotesConstants.PAYMENT_VOUCHER_CANCEL,
                    };

                    // Gọi hàm tạo bản ghi giao dịch nợ
                    await _debtTransactionHandler.Create(debtTransaction, currentUser);

                    // Cập nhật DebtAmount của Khách hàng mỗi bước insert vào sm_DebtTransaction = DebtAmount mới nhất trong sm_DebtTransaction
                    var debtAmount = await _dbContext.sm_DebtTransaction.Where(dt => dt.EntityId == latestDebtTransaction.EntityId && dt.EntityType == latestDebtTransaction.EntityType)
                        .OrderByDescending(dt => dt.CreatedOnDate).Select(dt => dt.DebtAmount).FirstOrDefaultAsync();

                    if (latestDebtTransaction.EntityType == DebtTransactionEntityTypesConstants.CUSTOMER)
                    {
                        var customer = await _dbContext.sm_Customer.FirstOrDefaultAsync(x => x.Id == latestDebtTransaction.EntityId);
                        if (customer != null)
                        {
                            customer.DebtAmount = debtAmount;
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                    else if (latestDebtTransaction.EntityType == DebtTransactionEntityTypesConstants.SUPPLIER)
                    {
                        var supplier = await _dbContext.sm_Supplier.FirstOrDefaultAsync(x => x.Id == latestDebtTransaction.EntityId);
                        if (supplier != null)
                        {
                            supplier.TotalDebtAmount = debtAmount;
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                }

                return Helper.CreateSuccessResponse(string.Format("Hủy kích hoạt {0} phiếu thành công.", ids.Count));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, ByUserIds: {@requestUserId}");
                return Utils.CreateExceptionResponseError(ex);
            }
        }
        /// <summary>
        /// Hàm tính toán số dư đầu kì/cuối kì, tổng thu/chi 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<Response<CashbookTransactionSummaryViewModel>> GetTransactionSummary(CashbookTransactionQueryModel query)
        {
            try
            {
                var predicate = BuildQueryTransaction(query);
                decimal totalInitialAmount = 0;
                var startDate = DateTime.Now;
                var endDate = DateTime.Now;

                var result = new CashbookTransactionSummaryViewModel();
                
                var cashbookTransactionEntity = _dbContext.sm_Cashbook_Transaction
                    .FirstOrDefault(x =>
                        x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction);

                if (query.DateRange != null && query.DateRange.Count() > 0)
                {
                    startDate = query.DateRange[0].Value.Date;
                    endDate = query.DateRange[1].Value.Date;
                    var listInitialCashbookTransaction = _dbContext.sm_Cashbook_Transaction
                    .Where(predicate.And(x => x.CreatedOnDate.Date < startDate && x.IsActive == StatusCashbookTransaction.COMPLETED 
                                                                               && x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction)).ToList(); 
                    
                    totalInitialAmount = listInitialCashbookTransaction.Sum(x => x.Amount);

                    var openingBalance = _dbContext.sm_Cashbook_Transaction.AsNoTracking().Where(predicate.And(x => x.CreatedOnDate.Date < startDate && x.IsActive == StatusCashbookTransaction.COMPLETED && x.TransactionTypeCode != CashbookTransactionConstants.InitialCashbookTransaction)).ToList();
                    var receiptList = openingBalance.Where(x => x.TransactionTypeCode == "THU" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);
                    var expenseList = openingBalance.Where(x => x.TransactionTypeCode == "CHI" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);

                    result.OpeningBalance = receiptList - expenseList + totalInitialAmount;

                    var closingBalanceQuery = _dbContext.sm_Cashbook_Transaction.Where(predicate.And(x =>
                        x.CreatedOnDate.Date >= startDate && x.CreatedOnDate.Date <= endDate &&
                        x.IsActive == StatusCashbookTransaction.COMPLETED));

                    result.TotalRevenueAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType || x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction).Sum(x => x.Amount);
                    result.TotalExpenseAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType).Sum(x => x.Amount);
                    if (cashbookTransactionEntity != null)
                    {
                        result.InitialInformation = new InitialInformationViewModel()
                        {
                            InitialAmount = _dbContext.sm_Cashbook_Transaction.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction).Sum(x => x.Amount),
                            InitialId = cashbookTransactionEntity.Id,
                            ReceiptDate = cashbookTransactionEntity.ReceiptDate,
                        };   
                    }
                    result.ClosingBalance = result.OpeningBalance + result.TotalRevenueAmount - result.TotalExpenseAmount;
                }

                if (query.ReceiptDateRange != null && query.ReceiptDateRange.Count() > 0)
                {
                    startDate = query.ReceiptDateRange[0].Value.Date;
                    endDate = query.ReceiptDateRange[1].Value.Date;
                    var listInitialCashbookTransaction = _dbContext.sm_Cashbook_Transaction
                    .Where(predicate.And(x => x.CreatedOnDate.Date < startDate && x.IsActive == StatusCashbookTransaction.COMPLETED
                                                                               && x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction)).ToList();

                    totalInitialAmount = listInitialCashbookTransaction.Sum(x => x.Amount);

                    var openingBalance = _dbContext.sm_Cashbook_Transaction.AsNoTracking().Where(predicate.And(x => x.ReceiptDate.Value.Date < startDate && x.IsActive == StatusCashbookTransaction.COMPLETED && x.TransactionTypeCode != CashbookTransactionConstants.InitialCashbookTransaction)).ToList();
                    var receiptList = openingBalance.Where(x => x.TransactionTypeCode == "THU" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);
                    var expenseList = openingBalance.Where(x => x.TransactionTypeCode == "CHI" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);

                    result.OpeningBalance = receiptList - expenseList + totalInitialAmount;

                    var closingBalanceQuery = _dbContext.sm_Cashbook_Transaction.Where(predicate.And(x =>
                        x.ReceiptDate.Value.Date >= startDate && x.ReceiptDate.Value.Date <= endDate &&
                        x.IsActive == StatusCashbookTransaction.COMPLETED));

                    result.TotalRevenueAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType || x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction).Sum(x => x.Amount);
                    result.TotalExpenseAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType).Sum(x => x.Amount);
                    if (cashbookTransactionEntity != null)
                    {
                        result.InitialInformation = new InitialInformationViewModel()
                        {
                            InitialAmount = _dbContext.sm_Cashbook_Transaction.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction).Sum(x => x.Amount),
                            InitialId = cashbookTransactionEntity.Id,
                            ReceiptDate = cashbookTransactionEntity.ReceiptDate,
                        };   
                    }
                    result.ClosingBalance = result.OpeningBalance + result.TotalRevenueAmount - result.TotalExpenseAmount;
                }


                //var listInitialCashbookTransaction = _dbContext.sm_Cashbook_Transaction
                //    .Where(predicate.And(x => x.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction)).ToList();

                //totalInitialAmount = listInitialCashbookTransaction.Sum(x => x.Amount);

                //var openingBalance = _dbContext.sm_Cashbook_Transaction.AsNoTracking().Where(predicate.And(x => x.CreatedOnDate.Date < startDate && x.IsActive == StatusCashbookTransaction.COMPLETED && x.TransactionTypeCode != CashbookTransactionConstants.InitialCashbookTransaction)).ToList();
                //var receiptList = openingBalance.Where(x => x.TransactionTypeCode == "THU" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);
                //var expenseList = openingBalance.Where(x => x.TransactionTypeCode == "CHI" && x.IsActive == StatusCashbookTransaction.COMPLETED).Sum(x => x.Amount);

                //result.OpeningBalance = receiptList - expenseList + totalInitialAmount;

                //var closingBalanceQuery = _dbContext.sm_Cashbook_Transaction.Where(predicate.And(x =>
                //    x.CreatedOnDate.Date >= startDate && x.CreatedOnDate.Date <= endDate &&
                //    x.IsActive == StatusCashbookTransaction.COMPLETED));

                //result.TotalRevenueAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType).Sum(x => x.Amount);
                //result.TotalExpenseAmount = closingBalanceQuery.Where(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType).Sum(x => x.Amount);
                //result.InitialAmount = listInitialCashbookTransaction.Sum(x => x.Amount);
                //result.ClosingBalance = result.OpeningBalance + result.TotalRevenueAmount - result.TotalExpenseAmount;

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<CashbookTransactionSummaryViewModel>(ex);
            }
        }



        private Expression<Func<sm_Cashbook_Transaction, bool>> BuildQueryTransaction(CashbookTransactionQueryModel query)
        {
            var predicate = PredicateBuilder.New<sm_Cashbook_Transaction>(true);

            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Reference.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (!string.IsNullOrEmpty(query.isActive))
            {
                predicate.And(s => s.IsActive == query.isActive);
            }


            if (query.ListPaymentMethodCode != null && query.ListPaymentMethodCode.Count() > 0)
            {
                predicate.And(x => query.ListPaymentMethodCode.Contains(x.PaymentMethodCode));
            }
            if (query.EntityTypeCodes != null && query.EntityTypeCodes.Count() > 0)
            {
                predicate.And(x => query.EntityTypeCodes.Contains(x.EntityTypeCode));
            }
            if (query.ListPurposeCode != null && query.ListPurposeCode.Count() > 0)
            {
                predicate.And(x => query.ListPurposeCode.Contains(x.PurposeCode));
            }

            if (!string.IsNullOrEmpty(query.TransactionTypeCode))
            {
                predicate.And(s => s.TransactionTypeCode == query.TransactionTypeCode);
            }
            if (query.ProjectId.HasValue)
            {
                predicate.And(s => s.ProjectId == query.ProjectId);
            }

            // Lọc theo DateRange
            if (query.DateRange != null && query.DateRange.Any())
            {
                var startDate = query.DateRange[0]?.Date;
                var endDate = query.DateRange[1]?.Date;

                if (startDate.HasValue && endDate.HasValue)
                {
                    // Điều kiện lọc ngoài DateRange
                    predicate.And(x => x.CreatedOnDate.Date >= startDate || x.CreatedOnDate.Date <= endDate);
                }
            }

            if (query.ReceiptDateRange != null && query.ReceiptDateRange.Any())
            {
                var startDate = query.ReceiptDateRange[0]?.Date;
                var endDate = query.ReceiptDateRange[1]?.Date;

                if (startDate.HasValue && endDate.HasValue)
                {
                    // Điều kiện lọc ngoài DateRange
                    predicate.And(x => x.ReceiptDate.Value.Date >= startDate && x.ReceiptDate.Value.Date <= endDate);
                }
            }

            return predicate;
        }

        private Expression<Func<sm_Cashbook_Transaction, bool>> BuildQuery(CashbookTransactionQueryModel query)
        {
            var predicate = PredicateBuilder.New<sm_Cashbook_Transaction>(true);

            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Reference.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (!string.IsNullOrEmpty(query.isActive))
                predicate.And(s => s.IsActive == query.isActive);


            if (query.ListPaymentMethodCode != null && query.ListPaymentMethodCode.Count() > 0)
            {
                predicate.And(x => query.ListPaymentMethodCode.Contains(x.PaymentMethodCode));
            }
            if (query.EntityTypeCodes != null && query.EntityTypeCodes.Count() > 0)
            {
                predicate.And(x => query.EntityTypeCodes.Contains(x.EntityTypeCode));
            }
            if (query.ListPurposeCode != null && query.ListPurposeCode.Count() > 0)
            {
                predicate.And(x => query.ListPurposeCode.Contains(x.PurposeCode));
            }
            if (query.ListReceiptCode != null && query.ListReceiptCode.Count() > 0)
            {
                predicate.And(x => query.ListReceiptCode.Contains(x.PurposeCode));
            }

            if (!string.IsNullOrEmpty(query.TransactionTypeCode))
            {
                predicate.And(s => s.TransactionTypeCode == query.TransactionTypeCode);
            }
            if (query.ProjectId.HasValue)
            {
                predicate.And(s => s.ProjectId == query.ProjectId);
            }

            if (query.DateRange != null && query.DateRange.Count() > 0)
            {
                if (query.DateRange[0].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date >= query.DateRange[0].Value.Date);

                if (query.DateRange[1].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date <= query.DateRange[1].Value.Date);
            }

            if (query.ContractId.HasValue)
            {
                predicate.And(s => s.ContractId == query.ContractId);
            }

            // List<string> IsActive
            if (query.StatusCodes != null && query.StatusCodes.Count() > 0)
            {
                predicate.And(x => query.StatusCodes.Contains(x.IsActive));
            }


            if (query.ReceiptDateRange != null && query.ReceiptDateRange.Any())
            {
                var startDate = query.ReceiptDateRange[0]?.Date;
                var endDate = query.ReceiptDateRange[1]?.Date;

                if (startDate.HasValue && endDate.HasValue)
                {
                    // Điều kiện lọc ngoài DateRange
                    predicate.And(x => x.ReceiptDate.Value.Date >= startDate && x.ReceiptDate.Value.Date <= endDate);
                }
            }

            return predicate;
        }

        /// <summary>
        /// Cập nhật dự án sau khi create phiếu thu/chi (nếu có)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>

        private async Task<Response> _updateProjectSummary(sm_Cashbook_Transaction entity)
        {
            if (!entity.ProjectId.HasValue)
            {
                return Helper.CreateBadRequestResponse();
            }
            var duAn = await _dbContext.mk_DuAn.FirstOrDefaultAsync(x => x.Id == entity.ProjectId);
            if (duAn == null)
            {
                return Helper.CreateBadRequestResponse<CashbookTransactionViewModel>(string.Format("Dự án {0} không tồn tại!", entity.ProjectId));
            }
            if (entity.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType)
            {
                duAn.TongHopChi = entity.IsActive == StatusCashbookTransaction.COMPLETED ? duAn.TongHopChi + entity.Amount : duAn.TongHopChi - entity.Amount;
            }
            else duAn.TongHopThu = entity.IsActive == StatusCashbookTransaction.COMPLETED ? duAn.TongHopThu + entity.Amount : duAn.TongHopThu - entity.Amount;

            await _dbContext.SaveChangesAsync();

            // Review 180924: không có SaveChanges

            return Helper.CreateSuccessResponse();
        }

        /// <summary>
        /// Tự tạo mã phiếu thu/chi nếu người dùng k nhập
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private async Task<string> _autoGeneratedCashbookCode(string type)
        {
            try
            {
                var prefixCode = "";
                
                if (type == CashbookTransactionConstants.ReceiptVoucherType)
                {
                    prefixCode = CashbookTransactionConstants.ReceiptVoucherPrefix;
                }
                else prefixCode = CashbookTransactionConstants.PaymentVoucherPrefix;
                
                if (type == CashbookTransactionConstants.InitialCashbookTransaction)
                {
                    prefixCode = CashbookTransactionConstants.InitialCashbookTransactionPrefix;
                }

                var code = prefixCode + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_Cashbook_Transaction.AsNoTracking().Where(s => s.Code.Contains(code)).OrderByDescending(x => x.CreatedOnDate).FirstOrDefaultAsync();


                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("", ex);
                return string.Empty;
            }
        }

        public async Task<Response<CashbookTransactionViewModel>> Update(Guid id, CashbookTransactionCreateUpdateModel model, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_Cashbook_Transaction.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<CashbookTransactionViewModel>(string.Format("Dữ liệu không tồn tại trong hệ thống!"));

                var isAllowEdit = await _dbContext.sm_Cashbook_Transaction.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id && x.IsActive != StatusCashbookTransaction.WAIT_TRANSFER 
                    && x.TransactionTypeCode != CashbookTransactionConstants.InitialCashbookTransaction);
                if (isAllowEdit != null)
                {
                    return Helper.CreateNotFoundResponse<CashbookTransactionViewModel>(string.Format("Không được chỉnh sửa phiếu này!"));
                }
                // Validate: Mã phiếu kiểm đã tồn tại
                var checkCode = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Code == model.Code && x.Id != id);
                if (checkCode != null)
                    return Helper.CreateBadRequestResponse<CashbookTransactionViewModel>
                        (string.Format("Mã {0} đã tồn tại!", model.Code));

                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.UserName;
                entity.LastModifiedOnDate = DateTime.Now;

                entity.Description = model.Description;
                entity.Reference = model.Reference;
                entity.Amount = model.Amount;

                if (entity.TransactionTypeCode == CashbookTransactionConstants.InitialCashbookTransaction)
                {
                    entity.ReceiptDate = model.ReceiptDate;
                    entity.CreatedOnDate = model.ReceiptDate;   
                }

                await _dbContext.SaveChangesAsync();
                var result = await GetById(entity.Id);

                if (result.IsSuccess)
                {
                    result.Message = "Chỉnh sửa thành công";
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<CashbookTransactionViewModel>(ex);
            }
        }

        /// <summary>
        /// Export Excel List of Cashbook Transaction (either Receipt Voucher or Payment Voucher)
        /// </summary>
        /// <param name="type">Loại phiếu, có thể là "receipt_voucher" hoặc "payment_voucher"</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Response<string>> ExportExcelListCashbookTransaction(string type)
        {
            try
            {
                // Kiểm tra loại phiếu hợp lệ
                if (type != CashbookTransactionConstants.ReceiptVoucherType && type != CashbookTransactionConstants.PaymentVoucherType)
                {
                    return Helper.CreateBadRequestResponse<string>("Loại phiếu không hợp lệ.");
                }

                // Kiểm tra loại phiếu (thu hoặc chi)
                bool isReceipt = type == CashbookTransactionConstants.ReceiptVoucherType;

                // Lấy danh sách phiếu thu hoặc phiếu chi từ cơ sở dữ liệu
                var receiptNotes = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                    .Include(x => x.Mk_DuAn)
                    .Where(x => x.TransactionTypeCode == (isReceipt
                        ? CashbookTransactionConstants.ReceiptVoucherType
                        : CashbookTransactionConstants.PaymentVoucherType))
                    .OrderByDescending(x => x.CreatedOnDate)
                    .ToListAsync();

                if (receiptNotes == null || receiptNotes.Count == 0)
                    return Helper.CreateNotFoundResponse<string>("Không có phiếu nào tồn tại trong hệ thống.");

                // Đặt tên file và đường dẫn template dựa trên loại phiếu
                var fileName = isReceipt
                    ? $"danh sách phiếu thu_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx"
                    : $"danh sách phiếu chi_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx";
                var filePath = Path.Combine(_staticsFolder, fileName);

                // Kiểm tra template path
                var templatePath = Path.Combine(_staticsFolder, isReceipt
                    ? "excel-template/ReceiptVouchersTemplate.xlsx"
                    : "excel-template/PaymentVouchersTemplate.xlsx");

                if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                    return Helper.CreateBadRequestResponse<string>("Không tìm thấy file template");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                // Mở file template excel và điền dữ liệu vào file
                using (var package = new ExcelPackage(new FileInfo(templatePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0]; // Giả sử dữ liệu trong worksheet đầu tiên

                    // Đặt độ rộng cố định cho cột A (cột STT) là 8
                    worksheet.Column(1).Width = 8;

                    // Điền tiêu đề bảng vào Excel (giả sử bắt đầu từ hàng thứ 4)
                    int startRow = 4;
                    int index = 1;

                    foreach (var note in receiptNotes)
                    {
                        worksheet.Cells[startRow, 1].Value = index; // STT
                        worksheet.Cells[startRow, 2].Value = note.CreatedOnDate.ToString("dd/MM/yyyy"); // Ngày tạo
                        worksheet.Cells[startRow, 3].Value = note.ReceiptDate?.ToString("dd/MM/yyyy"); // Ngày thu/chi
                        worksheet.Cells[startRow, 4].Value = note.LastModifiedOnDate?.ToString("dd/MM/yyyy"); // Ngày cập nhật
                        worksheet.Cells[startRow, 5].Value = CodeTypeCollection.Instance.FetchCode(note.PurposeCode, "vn", note.TenantId)?.Title ?? null; // Loại phiếu
                        worksheet.Cells[startRow, 6].Value = note.Code; // Mã phiếu
                        worksheet.Cells[startRow, 7].Value = note.IsActive switch
                        {
                            StatusCashbookTransaction.COMPLETED => CashbookTransactionConstants.CompletedStatusName,
                            StatusCashbookTransaction.CANCELED => CashbookTransactionConstants.CancelStatusName,
                            StatusCashbookTransaction.WAIT_TRANSFER => CashbookTransactionConstants.WaitTransferStatusName,
                        }; // Trạng thái
                        worksheet.Cells[startRow, 8].Value = note.Amount; // Số tiền thu/chi
                        worksheet.Cells[startRow, 9].Value = CodeTypeCollection.Instance.FetchCode(note.PaymentMethodCode, "vn", note.TenantId)?.Title ?? null; // Phương thức thanh toán
                        worksheet.Cells[startRow, 10].Value = note.Reference; // Tham chiếu
                        worksheet.Cells[startRow, 11].Value = note.CreatedByUserName; // Người tạo
                        worksheet.Cells[startRow, 12].Value = note.EntityCode; // Mã người nộp
                        worksheet.Cells[startRow, 13].Value = note.EntityName; // Tên người nộp
                        worksheet.Cells[startRow, 14].Value = note.EntityTypeName; // Nhóm người nộp
                        worksheet.Cells[startRow, 15].Value = note.OriginalDocumentCode; // Chứng từ gốc
                        worksheet.Cells[startRow, 16].Value = note.IsDebt ? CashbookTransactionConstants.IsDebtChanged : CashbookTransactionConstants.IsDebtUnChanged; // Thay đổi công nợ đối tượng nộp
                        worksheet.Cells[startRow, 17].Value = note.Mk_DuAn?.TenDuAn ?? null; // Tên dự án
                        worksheet.Cells[startRow, 18].Value = note.Note; // Mô tả

                        startRow++;
                        index++;
                    }

                    // Thêm đường viền cho các ô đã điền dữ liệu
                    int lastDataRow = startRow - 1; // Hàng cuối cùng có dữ liệu
                    using (var range = worksheet.Cells[4, 1, lastDataRow, 18]) // điều chỉnh cột cuối (18) tùy theo số lượng cột bạn có
                    {
                        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }

                    // Xóa các dòng thừa sau khi điền dữ liệu
                    int totalRows = worksheet.Dimension.End.Row;
                    if (totalRows > lastDataRow)
                    {
                        worksheet.DeleteRow(lastDataRow + 1, totalRows - lastDataRow);
                    }

                    // Auto-fit các cột từ cột thứ hai đến cột cuối cùng
                    worksheet.Cells[1, 2, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column].AutoFitColumns();

                    // Lưu file Excel đã điền dữ liệu
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                return Helper.CreateSuccessResponse<string>(filePath, "Xuất file thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<string>(ex);
            }
        }

        /// <summary>
        /// Xuất danh sách phiếu thu/chi thành file Excel dựa trên phân trang và lọc của trang hiện tại
        /// </summary>
        /// <param name="type">Loại phiếu, có thể là "receipt_voucher" hoặc "payment_voucher"</param>
        /// <param name="query">Các tham số lọc và phân trang</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Response<string>> ExportExcelListCashbookTransactionCurrentPage(string type, CashbookTransactionQueryModel query)
        {
            try
            {
                // Kiểm tra loại phiếu hợp lệ
                if (type != CashbookTransactionConstants.ReceiptVoucherType && type != CashbookTransactionConstants.PaymentVoucherType)
                {
                    return Helper.CreateBadRequestResponse<string>("Loại phiếu không hợp lệ.");
                }

                // Kiểm tra loại phiếu (thu hoặc chi)
                bool isReceipt = type == CashbookTransactionConstants.ReceiptVoucherType;

                // Tạo predicate để lọc dựa trên các tham số trong query
                var predicate = BuildQuery(query);

                // Lấy danh sách phiếu nhập hoặc phiếu xuất từ cơ sở dữ liệu dựa trên lọc và phân trang
                var receiptNotes = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                    .Include(x => x.Mk_DuAn)
                    .Where(x => x.TransactionTypeCode == (isReceipt
                        ? CashbookTransactionConstants.ReceiptVoucherType
                        : CashbookTransactionConstants.PaymentVoucherType))
                    .Where(predicate)
                    .OrderByDescending(x => x.CreatedOnDate)
                    .GetPageAsync(query);

                // Kiểm tra nếu không có dữ liệu trả về trong trang hiện tại
                if (receiptNotes == null || receiptNotes.Content == null || receiptNotes.Content.Count == 0)
                    return Helper.CreateNotFoundResponse<string>("Không có phiếu nào tồn tại trong hệ thống.");

                // Đặt tên file và đường dẫn template dựa trên loại phiếu
                var fileName = isReceipt
                    ? $"danh sách phiếu thu_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx"
                    : $"danh sách phiếu chi_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx";
                var filePath = Path.Combine(_staticsFolder, fileName);

                // Xác định đường dẫn template dựa trên loại phiếu
                var templatePath = Path.Combine(_staticsFolder, isReceipt
                    ? "excel-template/ReceiptVouchersTemplate.xlsx"
                    : "excel-template/PaymentVouchersTemplate.xlsx");

                if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                    return Helper.CreateBadRequestResponse<string>("Không tìm thấy file template");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                // Mở template Excel và điền dữ liệu vào file
                using (var package = new ExcelPackage(new FileInfo(templatePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0]; // Giả sử dữ liệu nằm trong worksheet đầu tiên

                    // Đặt độ rộng cố định cho cột A (cột STT) là 8
                    worksheet.Column(1).Width = 8;

                    // Điền dữ liệu vào bảng Excel (giả sử bắt đầu từ hàng thứ 4)
                    int startRow = 4;
                    int index = 1;

                    foreach (var note in receiptNotes.Content)
                    {
                        worksheet.Cells[startRow, 1].Value = index; // STT
                        worksheet.Cells[startRow, 2].Value = note.CreatedOnDate.ToString("dd/MM/yyyy"); // Ngày tạo
                        worksheet.Cells[startRow, 3].Value = note.ReceiptDate?.ToString("dd/MM/yyyy"); // Ngày thu/chi
                        worksheet.Cells[startRow, 4].Value = note.LastModifiedOnDate?.ToString("dd/MM/yyyy"); // Ngày cập nhật
                        worksheet.Cells[startRow, 5].Value = CodeTypeCollection.Instance.FetchCode(note.PurposeCode, "vn", note.TenantId)?.Title ?? null; // Loại phiếu
                        worksheet.Cells[startRow, 6].Value = note.Code; // Mã phiếu
                        worksheet.Cells[startRow, 7].Value = note.IsActive switch
                        {
                            StatusCashbookTransaction.COMPLETED => CashbookTransactionConstants.CompletedStatusName,
                            StatusCashbookTransaction.CANCELED => CashbookTransactionConstants.CancelStatusName,
                            StatusCashbookTransaction.WAIT_TRANSFER => CashbookTransactionConstants.WaitTransferStatusName,
                            _ => "Unknown Status" // hoặc để giá trị mặc định phù hợp
                        }; // Trạng thái
                        worksheet.Cells[startRow, 8].Value = note.Amount; // Số tiền thu/chi
                        worksheet.Cells[startRow, 9].Value = CodeTypeCollection.Instance.FetchCode(note.PaymentMethodCode, "vn", note.TenantId)?.Title ?? null; // Phương thức thanh toán
                        worksheet.Cells[startRow, 10].Value = note.Reference; // Tham chiếu
                        worksheet.Cells[startRow, 11].Value = note.CreatedByUserName; // Người tạo
                        worksheet.Cells[startRow, 12].Value = note.EntityCode; // Mã người nộp
                        worksheet.Cells[startRow, 13].Value = note.EntityName; // Tên người nộp
                        worksheet.Cells[startRow, 14].Value = note.EntityTypeName; // Nhóm người nộp
                        worksheet.Cells[startRow, 15].Value = note.OriginalDocumentCode; // Chứng từ gốc
                        worksheet.Cells[startRow, 16].Value = note.IsDebt ? CashbookTransactionConstants.IsDebtChanged : CashbookTransactionConstants.IsDebtUnChanged; // Thay đổi công nợ đối tượng nộp
                        worksheet.Cells[startRow, 17].Value = note.Mk_DuAn?.TenDuAn ?? null; // Tên dự án
                        worksheet.Cells[startRow, 18].Value = note.Note; // Mô tả

                        startRow++;
                        index++;
                    }

                    int lastDataRow = startRow - 1;
                    // Thêm đường viền cho các ô đã điền dữ liệu
                    using (var range = worksheet.Cells[4, 1, lastDataRow, 18]) // điều chỉnh cột cuối (18) tùy theo số lượng cột bạn có
                    {
                        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }

                    // Xóa các dòng thừa sau khi điền dữ liệu
                    int totalRows = worksheet.Dimension.End.Row;
                    if (totalRows > lastDataRow)
                    {
                        worksheet.DeleteRow(lastDataRow + 1, totalRows - lastDataRow);
                    }

                    // Tự động điều chỉnh kích thước các cột từ cột thứ hai đến cột cuối cùng
                    worksheet.Cells[1, 2, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column].AutoFitColumns();

                    // Lưu file Excel đã điền dữ liệu
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                return Helper.CreateSuccessResponse<string>(filePath, "Xuất file thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<string>(ex);
            }
        }

        /// <summary>
        /// Xuất danh sách báo cáo sổ quỹ thành file Excel dựa trên phân trang và lọc của trang hiện tại
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Response<string>> ExportExcelListVouchersPage(CashbookTransactionQueryModel query)
        {
            try
            {
                // Tạo predicate để lọc dựa trên các tham số trong query
                var predicate = BuildQuery(query);

                // Lấy danh sách phiếu thu hoặc phiếu chi từ cơ sở dữ liệu dựa trên lọc và phân trang
                var receiptNotes = await _dbContext.sm_Cashbook_Transaction.AsNoTracking()
                    .Include(x => x.Mk_DuAn)
                    .Where(predicate)
                    .OrderByDescending(x => x.CreatedOnDate)
                    .GetPageAsync(query);

                if (receiptNotes == null || receiptNotes.Content.Count == 0)
                    return Helper.CreateNotFoundResponse<string>("Không có phiếu nào tồn tại trong hệ thống.");

                // Lấy số dư đầu kỳ, tổng thu, tổng chi, và tồn cuối kỳ
                var summary = await GetTransactionSummary(query);

                var fileName = $"SoQuy_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx";
                var filePath = Path.Combine(_staticsFolder, fileName);
                var templatePath = Path.Combine(_staticsFolder, "excel-template/CashbookVouchersTemplate.xlsx");

                var currentDate = DateTime.Now;

                if (!File.Exists(templatePath))
                    return Helper.CreateBadRequestResponse<string>("Không tìm thấy file template");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(new FileInfo(templatePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    // Gán Ngày hiện tại vào dòng 6 cột 7
                    worksheet.Cells[6, 7].Value = $"Bắc Ninh, ngày {currentDate:dd} tháng {currentDate:MM} năm {currentDate:yyyy}";
                    worksheet.Cells[6, 7].Style.Font.Italic = true; // In nghiêng

                    // Gán DateRange theo query vào Excel
                    worksheet.Cells[11, 4].Value = query.DateRange[0].Value.ToString("dd/MM/yyyy");
                    worksheet.Cells[11, 6].Value = query.DateRange[1].Value.ToString("dd/MM/yyyy");

                    // Gán dữ liệu tổng kết vào hàng 14
                    worksheet.Cells[14, 1].Value = summary.Data.OpeningBalance; // Số dư đầu kỳ
                    worksheet.Cells[14, 3].Value = summary.Data.TotalRevenueAmount; // Tổng thu
                    worksheet.Cells[14, 5].Value = summary.Data.TotalExpenseAmount; // Tổng chi
                    worksheet.Cells[14, 7].Value = summary.Data.ClosingBalance; // Tồn cuối kỳ

                    int startRow = 18; // Dữ liệu bắt đầu từ hàng 18
                    int index = 1;

                    foreach (var note in receiptNotes.Content)
                    {
                        worksheet.Cells[startRow, 1].Value = index; // STT
                        worksheet.Cells[startRow, 2].Value = note.Code; // Mã phiếu
                        worksheet.Cells[startRow, 3].Value = CodeTypeCollection.Instance.FetchCode(note.PurposeCode, "vn", note.TenantId)?.Title ?? null; // Loại phiếu
                        worksheet.Cells[startRow, 4].Value = note.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? note.Amount : null; // Tiền thu
                        worksheet.Cells[startRow, 5].Value = note.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? note.Amount : null; // Tiền chi
                        worksheet.Cells[startRow, 6].Value = note.CreatedOnDate.ToString("dd/MM/yyyy"); // Ngày tạo
                        worksheet.Cells[startRow, 7].Value = note.CreatedByUserName; // Người tạo
                        worksheet.Cells[startRow, 8].Value = note.OriginalDocumentCode; // Mã chứng từ gốc

                        startRow++;
                        index++;
                    }

                    int lastDataRow = startRow - 1;

                    // Thêm đường viền cho các ô đã điền dữ liệu
                    using (var range = worksheet.Cells[18, 1, lastDataRow, 8]) // điều chỉnh cột cuối (8) tùy theo số lượng cột bạn có
                    {
                        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }

                    // Giữ nguyên các dòng cố định phía dưới
                    int footerStartRow = lastDataRow + 4; // Đặt các dòng footer bắt đầu từ dưới bảng dữ liệu

                    worksheet.Cells[footerStartRow, 7].Value = $"Bắc Ninh, ngày {currentDate:dd} tháng {currentDate:MM} năm {currentDate:yyyy}";
                    worksheet.Cells[footerStartRow, 7].Style.Font.Italic = true; // In nghiêng
                    worksheet.Cells[footerStartRow, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Căn giữa

                    worksheet.Cells[footerStartRow + 2, 2].Value = "Người phê duyệt";
                    worksheet.Cells[footerStartRow + 2, 2].Style.Font.Bold = true; // In đậm
                    worksheet.Cells[footerStartRow + 2, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Căn giữa

                    worksheet.Cells[footerStartRow + 2, 7].Value = "Người lập biểu";
                    worksheet.Cells[footerStartRow + 2, 7].Style.Font.Bold = true; // In đậm
                    worksheet.Cells[footerStartRow + 2, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Căn giữa

                    worksheet.Cells[footerStartRow + 3, 2].Value = "(Ký, ghi rõ họ tên)";
                    worksheet.Cells[footerStartRow + 3, 2].Style.Font.Italic = true; // In nghiêng
                    worksheet.Cells[footerStartRow + 3, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Căn giữa

                    worksheet.Cells[footerStartRow + 3, 7].Value = "(Ký, ghi rõ họ tên)";
                    worksheet.Cells[footerStartRow + 3, 7].Style.Font.Italic = true; // In nghiêng
                    worksheet.Cells[footerStartRow + 3, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Căn giữa

                    // Tự động điều chỉnh kích thước các cột từ cột thứ hai đến cột cuối cùng
                    worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column].AutoFitColumns();

                    await package.SaveAsAsync(new FileInfo(filePath));
                    return Helper.CreateSuccessResponse<string>(filePath, "Xuất file thành công");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<string>(ex);
            }
        }


        public async Task<Response<CashbookTransactionViewModel>> PayInvoiceAsync(Guid id, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_Cashbook_Transaction.FirstOrDefaultAsync(x => x.Id == id && x.IsActive == StatusCashbookTransaction.WAIT_TRANSFER);

                if (entity == null)
                    return Helper.CreateNotFoundResponse<CashbookTransactionViewModel>(string.Format("Phiếu không tồn tại trong hệ thống!"));

                entity.IsActive = StatusCashbookTransaction.COMPLETED;
                entity.LastModifiedOnDate = DateTime.Now;

                if (entity.AdvanceRequestId.HasValue)
                {
                    var advanceRequest = await _dbContext.sm_AdvanceRequest.FirstOrDefaultAsync(x => x.Id == entity.AdvanceRequestId);
                    if (advanceRequest != null)
                    {
                        advanceRequest.StatusCode = AdvanceRequestConstants.StatusCode.COMPLETED;
                        advanceRequest.StatusName = AdvanceRequestConstants.StatusName.COMPLETED;
                        advanceRequest.StatusColor = AdvanceRequestConstants.StatusColor.COMPLETED;
                        advanceRequest.LastModifiedOnDate = DateTime.Now;
                        advanceRequest.LastModifiedByUserId = entity.LastModifiedByUserId;
                        advanceRequest.LastModifiedByUserName = entity.LastModifiedByUserName;
                        advanceRequest.AdvanceRequestHistories = new List<jsonb_AdvanceRequestHistory>
                        {
                            new jsonb_AdvanceRequestHistory
                            {
                                Id = Guid.NewGuid(),
                                Action = "đã tạo phiếu chi thành công từ yêu cầu",
                                CreatedOnDate = DateTime.Now,
                                CreatedByUserName = currentUser.FullName
                            }
                        };
                    }
                }

                await _dbContext.SaveChangesAsync();
                return Helper.CreateSuccessResponse(_mapper.Map<CashbookTransactionViewModel>(entity), "Thanh toán thành công");

            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<CashbookTransactionViewModel>(ex);
            }
        }

        /// <summary>
        /// Báo cáo dòng tiền theo năm
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Response<CashFlowReportViewModel>> GetCashFlowReportAsync(CashbookTransactionQueryModel query)
        {
            try
            {

                var predicate = BuildQuery(query);

                var result = new CashFlowReportViewModel();

                // Lấy danh sách phiếu thu/chi từ cơ sở dữ liệu
                var cashbookTransactions = await _dbContext.sm_Cashbook_Transaction.Where(predicate.And(x =>
                    x.CreatedOnDate.Year >= query.YearDate.Year && x.CreatedOnDate.Year <= query.YearDate.Year &&
                    x.IsActive == StatusCashbookTransaction.COMPLETED)).ToListAsync();

                // Lấy danh sách các loại phiếu trong CodeType có code "purposeReceipt"
                var purposeReceipts = await _dbContext.sm_CodeType.Where(x => x.Type == "purposeReceipt").ToListAsync();

                // Lấy danh sách các loại phiếu trong CodeType có code "EXPENDITURE_PURPOSE"
                var expenditurePurposes = await _dbContext.sm_CodeType.Where(x => x.Type == "EXPENDITURE_PURPOSE").ToListAsync();

                // tháng 12 của năm trước
                DateTime lastMonth = new DateTime(query.YearDate.Year - 1, 12, 1);
                var lastMonthBalance = await GetTransactionSummary(new CashbookTransactionQueryModel()
                {
                    DateRange = new DateTime?[] { lastMonth, lastMonth.AddMonths(1).AddDays(-1) }
                });

                var openingBalance = new CashFlowReportItemViewModel();
                openingBalance.Title = "Tiền mặt hiện có đầu tháng";
                openingBalance.StartAmount = lastMonthBalance.Data.ClosingBalance;

                // For each month, calculate the opening balance
                for (int i = 1; i <= 12; i++)
                {
                    switch (i)
                    {
                        case 1:
                            DateTime dateTime = new DateTime(query.YearDate.Year, 1, 1);
                            var getTransactionSummary = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { dateTime, dateTime.AddMonths(1).AddDays(-1) }
                            });
                            openingBalance.JanuaryAmount = getTransactionSummary.Data.OpeningBalance;
                            break;
                        case 2:
                            var getTransactionSummary2 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 2, 1), new DateTime(query.YearDate.Year, 2, 28) }
                            });
                            openingBalance.FebruaryAmount = getTransactionSummary2.Data.OpeningBalance;
                            break;
                        case 3:
                            var getTransactionSummary3 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 3, 1), new DateTime(query.YearDate.Year, 3, 31) }
                            });
                            openingBalance.MarchAmount = getTransactionSummary3.Data.OpeningBalance;
                            break;
                        case 4:
                            var getTransactionSummary4 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 4, 1), new DateTime(query.YearDate.Year, 4, 30) }
                            });
                            openingBalance.AprilAmount = getTransactionSummary4.Data.OpeningBalance;
                            break;
                        case 5:
                            var getTransactionSummary5 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 5, 1), new DateTime(query.YearDate.Year, 5, 31) }
                            });
                            openingBalance.MayAmount = getTransactionSummary5.Data.OpeningBalance;
                            break;
                        case 6:
                            var getTransactionSummary6 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 6, 1), new DateTime(query.YearDate.Year, 6, 30) }
                            });
                            openingBalance.JuneAmount = getTransactionSummary6.Data.OpeningBalance;
                            break;
                        case 7:
                            var getTransactionSummary7 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 7, 1), new DateTime(query.YearDate.Year, 7, 31) }
                            });
                            openingBalance.JulyAmount = getTransactionSummary7.Data.OpeningBalance;
                            break;
                        case 8:
                            var getTransactionSummary8 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 8, 1), new DateTime(query.YearDate.Year, 8, 31) }
                            });
                            openingBalance.AugustAmount = getTransactionSummary8.Data.OpeningBalance;
                            break;
                        case 9:
                            var getTransactionSummary9 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 9, 1), new DateTime(query.YearDate.Year, 9, 30) }
                            });
                            openingBalance.SeptemberAmount = getTransactionSummary9.Data.OpeningBalance;
                            break;
                        case 10:
                            var getTransactionSummary10 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 10, 1), new DateTime(query.YearDate.Year, 10, 31) }
                            });
                            openingBalance.OctoberAmount = getTransactionSummary10.Data.OpeningBalance;
                            break;
                        case 11:
                            var getTransactionSummary11 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 11, 1), new DateTime(query.YearDate.Year, 11, 30) }
                            });
                            openingBalance.NovemberAmount = getTransactionSummary11.Data.OpeningBalance;
                            break;
                        case 12:
                            var getTransactionSummary12 = await GetTransactionSummary(new CashbookTransactionQueryModel()
                            {
                                DateRange = new DateTime?[] { new DateTime(query.YearDate.Year, 12, 1), new DateTime(query.YearDate.Year, 12, 31) }
                            });
                            openingBalance.DecemberAmount = getTransactionSummary12.Data.OpeningBalance;
                            break;
                    }
                }

                openingBalance.TotalAmount = openingBalance.StartAmount ?? 0;

                // Tính toán dòng tiền theo năm
                var cashFlow = cashbookTransactions.GroupBy(x => x.CreatedOnDate.Year)
                    .Select(x => new CashFlowReportViewModel
                    {

                        OpeningCashBalance = openingBalance,
                        ReceiptVouchers = new ReceiptVouchersViewModel()
                        {
                            Items = purposeReceipts.Select((purpose, index) => new CashFlowReportItemViewModel()
                            {
                                LineNumber = index + 1,
                                Title = purpose.Title,
                                JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                TotalAmount = x.Where(y => y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)
                            }).ToList(),
                            ReceiptTotalAmount = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng thu",
                                JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)
                                //JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0),
                                //TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)
                            },
                            TotalCashBalance = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng Tiền mặt Hiện có (trước khi rút tiền mặt)",
                                JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JanuaryAmount,
                                FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.FebruaryAmount,
                                MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.MarchAmount,
                                AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.AprilAmount,
                                MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.MayAmount,
                                JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JuneAmount,
                                JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JulyAmount,
                                AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.AugustAmount,
                                SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.SeptemberAmount,
                                OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.OctoberAmount,
                                NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.NovemberAmount,
                                DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.DecemberAmount,
                                TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.TotalAmount
                                //JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JanuaryAmount,
                                //FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.FebruaryAmount,
                                //MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.MarchAmount,
                                //AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.AprilAmount,
                                //MayAmount = x.Where(y => y.CreatedOnDate.Month == 5).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.MayAmount,
                                //JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JuneAmount,
                                //JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JulyAmount,
                                //AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.AugustAmount,
                                //SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.SeptemberAmount,
                                //OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.OctoberAmount,
                                //NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.NovemberAmount,
                                //DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.DecemberAmount,
                                //TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType).Sum(y => y.Amount) + openingBalance.TotalAmount
                            },
                        },
                        PaymentVouchers = new PaymentVouchersViewModel()
                        {
                            Items = expenditurePurposes.Select((purpose, index) => new CashFlowReportItemViewModel()
                            {
                                LineNumber = index + 1,
                                Title = purpose.Title,
                                JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                TotalAmount = x.Where(y => y.PurposeCode == purpose.Code && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)
                            }).ToList(),
                            PaymentTotalAmount = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng chi",
                                JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                MayAmount = x.Where(y => y.CreatedOnDate.Month == 5 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),
                                TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)
                                //JanuaryAmount = x.Where(y => y.CreatedOnDate.Month == 1).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //FebruaryAmount = x.Where(y => y.CreatedOnDate.Month == 2).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //MarchAmount = x.Where(y => y.CreatedOnDate.Month == 3).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //AprilAmount = x.Where(y => y.CreatedOnDate.Month == 4).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //MayAmount = x.Where(y => y.CreatedOnDate.Month == 5).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //JuneAmount = x.Where(y => y.CreatedOnDate.Month == 6).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //JulyAmount = x.Where(y => y.CreatedOnDate.Month == 7).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //AugustAmount = x.Where(y => y.CreatedOnDate.Month == 8).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //SeptemberAmount = x.Where(y => y.CreatedOnDate.Month == 9).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //OctoberAmount = x.Where(y => y.CreatedOnDate.Month == 10).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //NovemberAmount = x.Where(y => y.CreatedOnDate.Month == 11).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //DecemberAmount = x.Where(y => y.CreatedOnDate.Month == 12).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),
                                //TotalAmount = x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType).Sum(y => y.Amount)
                            },
                            TotalCashBalance = new CashFlowReportItemViewModel()
                            {
                                Title = "Tình hình Tiền mặt Hiện tại (Sau khi chi)",
                                JanuaryAmount = (x.Where(y => y.CreatedOnDate.Month == 1 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JanuaryAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 1 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                FebruaryAmount = (x.Where(y => y.CreatedOnDate.Month == 2 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.FebruaryAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 2 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                MarchAmount = (x.Where(y => y.CreatedOnDate.Month == 3 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.MarchAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 3 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                AprilAmount = (x.Where(y => y.CreatedOnDate.Month == 4 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.AprilAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 4 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                MayAmount = (x.Where(y => y.CreatedOnDate.Month == 5 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.MayAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 5 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                JuneAmount = (x.Where(y => y.CreatedOnDate.Month == 6 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JuneAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 6 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                JulyAmount = (x.Where(y => y.CreatedOnDate.Month == 7 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.JulyAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 7 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                AugustAmount = (x.Where(y => y.CreatedOnDate.Month == 8 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.AugustAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 8 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                SeptemberAmount = (x.Where(y => y.CreatedOnDate.Month == 9 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.SeptemberAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 9 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                OctoberAmount = (x.Where(y => y.CreatedOnDate.Month == 10 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.OctoberAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 10 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                NovemberAmount = (x.Where(y => y.CreatedOnDate.Month == 11 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.NovemberAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 11 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                DecemberAmount = (x.Where(y => y.CreatedOnDate.Month == 12 && y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.DecemberAmount)
                                - x.Where(y => y.CreatedOnDate.Month == 12 && y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount),

                                TotalAmount = (x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount) + openingBalance.TotalAmount)
                                - x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && y.IsActive == StatusCashbookTransaction.COMPLETED).Sum(y => y.Amount)

                                //JanuaryAmount = (x.Where(y => y.CreatedOnDate.Month == 1).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JanuaryAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 1).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //FebruaryAmount = (x.Where(y => y.CreatedOnDate.Month == 2).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.FebruaryAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 2).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //MarchAmount = (x.Where(y => y.CreatedOnDate.Month == 3).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.MarchAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 3).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //AprilAmount = (x.Where(y => y.CreatedOnDate.Month == 4).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.AprilAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 4).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //MayAmount = (x.Where(y => y.CreatedOnDate.Month == 5).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.MayAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 5).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //JuneAmount = (x.Where(y => y.CreatedOnDate.Month == 6).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JuneAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 6).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //JulyAmount = (x.Where(y => y.CreatedOnDate.Month == 7).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.JulyAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 7).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //AugustAmount = (x.Where(y => y.CreatedOnDate.Month == 8).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.AugustAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 8).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //SeptemberAmount = (x.Where(y => y.CreatedOnDate.Month == 9).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.SeptemberAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 9).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //OctoberAmount = (x.Where(y => y.CreatedOnDate.Month == 10).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.OctoberAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 10).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //NovemberAmount = (x.Where(y => y.CreatedOnDate.Month == 11).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.NovemberAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 11).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //DecemberAmount = (x.Where(y => y.CreatedOnDate.Month == 12).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? y.Amount : 0) + openingBalance.DecemberAmount)
                                //- x.Where(y => y.CreatedOnDate.Month == 12).Sum(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? y.Amount : 0),

                                //TotalAmount = (x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType).Sum(y => y.Amount) + openingBalance.TotalAmount)
                                //- x.Where(y => y.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType).Sum(y => y.Amount)
                            },
                        }
                    }).ToList();

                // If no results, return a default empty report
                if (!cashFlow.Any())
                {
                    cashFlow.Add(new CashFlowReportViewModel
                    {
                        OpeningCashBalance = openingBalance,
                        ReceiptVouchers = new ReceiptVouchersViewModel()
                        {
                            Items = purposeReceipts.Select(purpose => new CashFlowReportItemViewModel()
                            {
                                Title = purpose.Title,
                                JanuaryAmount = 0,
                                FebruaryAmount = 0,
                                MarchAmount = 0,
                                AprilAmount = 0,
                                MayAmount = 0,
                                JuneAmount = 0,
                                JulyAmount = 0,
                                AugustAmount = 0,
                                SeptemberAmount = 0,
                                OctoberAmount = 0,
                                NovemberAmount = 0,
                                DecemberAmount = 0,
                                TotalAmount = 0
                            }).ToList(),
                            ReceiptTotalAmount = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng thu",
                                JanuaryAmount = 0,
                                FebruaryAmount = 0,
                                MarchAmount = 0,
                                AprilAmount = 0,
                                MayAmount = 0,
                                JuneAmount = 0,
                                JulyAmount = 0,
                                AugustAmount = 0,
                                SeptemberAmount = 0,
                                OctoberAmount = 0,
                                NovemberAmount = 0,
                                DecemberAmount = 0,
                                TotalAmount = 0
                            },
                            TotalCashBalance = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng Tiền mặt Hiện có (trước khi rút tiền mặt)",
                                JanuaryAmount = 0 + openingBalance.JanuaryAmount,
                                FebruaryAmount = 0 + openingBalance.FebruaryAmount,
                                MarchAmount = 0 + openingBalance.MarchAmount,
                                AprilAmount = 0 + openingBalance.AprilAmount,
                                MayAmount = 0 + openingBalance.MayAmount,
                                JuneAmount = 0 + openingBalance.JuneAmount,
                                JulyAmount = 0 + openingBalance.JulyAmount,
                                AugustAmount = 0 + openingBalance.AugustAmount,
                                SeptemberAmount = 0 + openingBalance.SeptemberAmount,
                                OctoberAmount = 0 + openingBalance.OctoberAmount,
                                NovemberAmount = 0 + openingBalance.NovemberAmount,
                                DecemberAmount = 0 + openingBalance.DecemberAmount,
                                TotalAmount = 0 + openingBalance.TotalAmount
                            },
                        },
                        PaymentVouchers = new PaymentVouchersViewModel()
                        {
                            Items = expenditurePurposes.Select(purpose => new CashFlowReportItemViewModel()
                            {
                                Title = purpose.Title,
                                JanuaryAmount = 0,
                                FebruaryAmount = 0,
                                MarchAmount = 0,
                                AprilAmount = 0,
                                MayAmount = 0,
                                JuneAmount = 0,
                                JulyAmount = 0,
                                AugustAmount = 0,
                                SeptemberAmount = 0,
                                OctoberAmount = 0,
                                NovemberAmount = 0,
                                DecemberAmount = 0,
                                TotalAmount = 0
                            }).ToList(),
                            PaymentTotalAmount = new CashFlowReportItemViewModel()
                            {
                                Title = "Tổng chi",
                                JanuaryAmount = 0,
                                FebruaryAmount = 0,
                                MarchAmount = 0,
                                AprilAmount = 0,
                                MayAmount = 0,
                                JuneAmount = 0,
                                JulyAmount = 0,
                                AugustAmount = 0,
                                SeptemberAmount = 0,
                                OctoberAmount = 0,
                                NovemberAmount = 0,
                                DecemberAmount = 0,
                                TotalAmount = 0
                            },
                            TotalCashBalance = new CashFlowReportItemViewModel()
                            {
                                Title = "Tình hình Tiền mặt Hiện tại (Sau khi chi)",
                                JanuaryAmount = 0 + openingBalance.JanuaryAmount,
                                FebruaryAmount = 0 + openingBalance.FebruaryAmount,
                                MarchAmount = 0 + openingBalance.MarchAmount,
                                AprilAmount = 0 + openingBalance.AprilAmount,
                                MayAmount = 0 + openingBalance.MayAmount,
                                JuneAmount = 0 + openingBalance.JuneAmount,
                                JulyAmount = 0 + openingBalance.JulyAmount,
                                AugustAmount = 0 + openingBalance.AugustAmount,
                                SeptemberAmount = 0 + openingBalance.SeptemberAmount,
                                OctoberAmount = 0 + openingBalance.OctoberAmount,
                                NovemberAmount = 0 + openingBalance.NovemberAmount,
                                DecemberAmount = 0 + openingBalance.DecemberAmount,
                                TotalAmount = 0 + openingBalance.TotalAmount
                            },
                        }
                    });
                }

                result = cashFlow.FirstOrDefault();

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<CashFlowReportViewModel>(ex);
            }
        }

        public async Task<Response<CashbookDashboardViewModel>> DashboardWithNoFilter(CashbookDashboardFilterDatetimeModel filter)
        {
            try
            {
                var startDate = filter.DateRange[0].Value.StartOfDay();
                var endDate = filter.DateRange[1].Value.EndOfDay();

                // Tính tổng quỹ (fundBalance)
                var fundBalance = await _dbContext.sm_Cashbook_Transaction
                    .Where(x => x.IsActive == StatusCashbookTransaction.COMPLETED)
                    .Select(x => new
                    {
                        x.TransactionTypeCode,
                        x.Amount
                    })
                    .ToListAsync();

                var fund = fundBalance
                    .Where(x => x.TransactionTypeCode == "INITIAL_TRANSACTION")
                    .Sum(x => x.Amount)
                    + fundBalance
                    .Where(x => x.TransactionTypeCode == "THU")
                    .Sum(x => x.Amount)
                    - fundBalance
                    .Where(x => x.TransactionTypeCode == "CHI")
                    .Sum(x => x.Amount);

                var finalFund = Math.Round(fund / 1000000, 2);

                // Lấy mục đích thu và chi
                var receiptsPurpose = await _dbContext.sm_CodeType
                    .Where(x => x.Type == CodeTypeConstants.Purpose_Receipt)
                    .Select(x => new { x.Code, x.Title })
                    .ToListAsync();

                var expendituresPurpose = await _dbContext.sm_CodeType
                    .Where(x => x.Type == CodeTypeConstants.Expenditure_Purpose)
                    .Select(x => new { x.Code, x.Title })
                    .ToListAsync();

                // Tính tổng theo mục đích
                var datasWithPurpose = await _dbContext.sm_Cashbook_Transaction
                    .Where(x => x.PurposeCode != null && x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate && x.IsActive == StatusCashbookTransaction.COMPLETED)
                    .GroupBy(x => x.PurposeCode)
                    .Select(x => new
                    {
                        x.Key,
                        TotalAmount = x.Sum(y => y.Amount)
                    }).ToListAsync();

                // Tính dữ liệu cho receiptsPurpose và expendituresPurpose
                var receiptsDataWithPurpose = receiptsPurpose
                    .Select(cd => new
                    {
                        Description = cd.Title,
                        TotalAmount = Math.Round((datasWithPurpose.FirstOrDefault(pt => pt.Key == cd.Code)?.TotalAmount ?? 0) / 1000000, 2)
                    }).ToList();

                var expendituresDataWithPurpose = expendituresPurpose
                    .Select(cd => new
                    {
                        Description = cd.Title,
                        TotalAmount = Math.Round((datasWithPurpose.FirstOrDefault(pt => pt.Key == cd.Code)?.TotalAmount ?? 0) / 1000000, 2)
                    }).ToList();

                var data = new
                {
                    Fund = finalFund, // Quỹ
                    ReceiptsPurpose = receiptsDataWithPurpose,
                    ExpendituresDataWithPurpose = expendituresDataWithPurpose,
                };

                return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
                {
                    DashboardData = data
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
            }
        }

        //public async Task<Response<CashbookDashboardViewModel>> DashboardWithNoFilter(CashbookDashboardFilterDatetimeModel filter)
        //{
        //    try
        //    {
        //        var startDate = filter.DateRange[0].Value.StartOfDay();
        //        var endDate = filter.DateRange[1].Value.EndOfDay();

        //        // Tính tổng quỹ (fundBalance)
        //        var fundBalance = await _dbContext.sm_Cashbook_Transaction.Where(x => x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate && x.IsActive == StatusCashbookTransaction.COMPLETED).Select(x => new { x.TransactionTypeCode,x.Amount}).ToListAsync();

        //        // Tính tổng Fund từ các giao dịch
        //        var fund = fundBalance
        //            .Where(x => x.TransactionTypeCode == "INITIAL_TRANSACTION")
        //            .Sum(x => x.Amount) // Cộng dồn cho giao dịch loại INITIAL_TRANSACTION

        //        + fundBalance
        //            .Where(x => x.TransactionTypeCode == "THU")
        //            .Sum(x => x.Amount) // Cộng dồn cho giao dịch loại THU

        //        - fundBalance
        //            .Where(x => x.TransactionTypeCode == "CHI")
        //            .Sum(x => x.Amount); // Trừ đi cho giao dịch loại CHI

        //        // Chia cho triệu nếu cần
        //        var finalFund = fund / 1000000; // Để hiển thị dưới đơn vị triệu đồng


        //        // Lấy mục đích thu và chi
        //        var receiptsPurpose = await _dbContext.sm_CodeType
        //            .Where(x => x.Type == CodeTypeConstants.Purpose_Receipt)
        //            .Select(x => new { x.Code, x.Title })
        //            .ToListAsync();

        //        var expendituresPurpose = await _dbContext.sm_CodeType
        //            .Where(x => x.Type == CodeTypeConstants.Expenditure_Purpose && x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate)
        //            .Select(x => new { x.Code, x.Title })
        //            .ToListAsync();

        //        // Tính tổng theo mục đích
        //        var datasWithPurpose = await _dbContext.sm_Cashbook_Transaction
        //            .Where(x => x.PurposeCode != null && x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate)
        //            .GroupBy(x => x.PurposeCode)
        //            .Select(x => new
        //            {
        //                x.Key,
        //                TotalAmount = x.Sum(y => y.Amount)
        //            }).ToListAsync();

        //        // Tính dữ liệu cho receiptsPurpose và expendituresPurpose
        //        var receiptsDataWithPurpose = receiptsPurpose
        //            .Select(cd => new
        //            {
        //                Description = cd.Title,
        //                TotalAmount = (datasWithPurpose
        //                    .FirstOrDefault(pt => pt.Key.Contains(cd.Code))?.TotalAmount ?? 0) / 1000000
        //            })
        //            .ToList();

        //        var expendituresDataWithPurpose = expendituresPurpose.Select(cd => new { Description = cd.Title,TotalAmount = (datasWithPurpose.FirstOrDefault(pt => pt.PurposeCode == cd.Code)?.TotalAmount ?? 0) / 1000000  }).ToList();


        //        var data = new
        //        {
        //            Fund = finalFund / 1000000, // Quỹ
        //            ReceiptsPurpose = receiptsDataWithPurpose,
        //            ExpendituresDataWithPurpose = expendituresDataWithPurpose,
        //        };

        //        return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
        //        {
        //            DashboardData = data
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, string.Empty);
        //        return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
        //    }
        //}

        //public async Task<Response<CashbookDashboardViewModel>> DashboardWithNoFilter(CashbookDashboardFilterDatetimeModel filter)
        //{
        //    try
        //    {
        //        var startDate = filter.DateRange[0].Value.StartOfDay();
        //        var endDate = filter.DateRange[1].Value.EndOfDay();
        //        var fundBalance = _dbContext.sm_Cashbook_Transaction
        //            .Where(x => x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate && x.IsActive == StatusCashbookTransaction.COMPLETED)
        //            .GroupBy(x => new { x.Id, x.Amount, x.TransactionTypeCode }).Select(
        //                x =>
        //                    new
        //                    {
        //                        Fund = x.Where(y => y.TransactionTypeCode.Equals("INITIAL_TRANSACTION"))
        //                                   .Sum(y => y.Amount)
        //                               + x.Where(y => y.TransactionTypeCode.Equals("THU")).Sum(y => y.Amount)
        //                               - x.Where(y => y.TransactionTypeCode.Equals("CHI")).Sum(y => y.Amount)
        //                    }).Sum(x => x.Fund);

        //        var receiptsPurpose = await _dbContext.sm_CodeType
        //            .Where(x => x.Type == CodeTypeConstants.Purpose_Receipt)
        //            .GroupBy(x => new
        //            {
        //                x.Code,
        //                x.Title
        //            })
        //            .Select(x => new { x.Key.Code, x.Key.Title }).ToListAsync();

        //        var expendituresPurpose = await _dbContext.sm_CodeType
        //            .Where(x => x.Type == CodeTypeConstants.Expenditure_Purpose && x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate)
        //            .GroupBy(x => new
        //            {
        //                x.Code,
        //                x.Title
        //            })
        //            .Select(x => new { x.Key.Code, x.Key.Title }).ToListAsync();

        //        var datasWithPurpose = await
        //            _dbContext.sm_Cashbook_Transaction.Where(x => x.PurposeCode != null && x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate)
        //                .GroupBy(x => new { x.PurposeCode }).Select(x => new
        //                {
        //                    x.Key.PurposeCode,
        //                    TotalAmount = x.Sum(y => y.Amount)
        //                }).ToListAsync();

        //        var receiptsDataWithPurpose = receiptsPurpose
        //            .SelectMany(cd => datasWithPurpose
        //                .Where(pt => pt.PurposeCode.Contains(cd.Code))
        //                .Select(pt => new
        //                {
        //                    Description = cd.Title,
        //                    TotalAmount = (pt.TotalAmount / 1000000).ToString("0.000")
        //                })
        //            ).ToList();
        //        var expendituresDataWithPurpose = expendituresPurpose
        //            .SelectMany(cd => datasWithPurpose
        //                .Where(pt => pt.PurposeCode.Contains(cd.Code))
        //                .Select(pt => new
        //                {
        //                    Description = cd.Title,
        //                    TotalAmount = (pt.TotalAmount / 1000000).ToString("0.000")
        //                })
        //            ).ToList();

        //        var data = new
        //        {
        //            Fund = fundBalance / 1000000,
        //            ReceiptsPurpose = receiptsDataWithPurpose,
        //            ExpendituresDataWithPurpose = expendituresDataWithPurpose,
        //        };

        //        return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
        //        {
        //            DashboardData = data
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, string.Empty);
        //        return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
        //    }
        //}

        public async Task<Response<CashbookDashboardViewModel>> DashboardWithTotalFilter(CashbookDashboardFilterDatetimeModel filter)
        {
            try
            {
                var startDate = filter.DateRange[0].Value.StartOfDay();
                var endDate = filter.DateRange[1].Value.EndOfDay();

                // Tổng hiện tại
                var totals = await _dbContext.sm_Cashbook_Transaction
                    .Where(x => x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate && x.IsActive == StatusCashbookTransaction.COMPLETED)
                    .GroupBy(x => 1)
                    .Select(g => new
                    {
                        TotalReceiptsAmount = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? x.Amount : 0),
                        TotalExpendituresAmount = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? x.Amount : 0)
                    })
                    .FirstOrDefaultAsync();

                var totalReceipts = totals?.TotalReceiptsAmount ?? 0;
                var totalExpenditures = totals?.TotalExpendituresAmount ?? 0;

                // === Tổng ngày hôm qua ===
                var yesterdayStart = startDate.AddDays(-1);
                var yesterdayEnd = endDate.AddDays(-1);

                var yesterdayTotals = await _dbContext.sm_Cashbook_Transaction
                    .Where(x => x.CreatedOnDate >= yesterdayStart && x.CreatedOnDate <= yesterdayEnd && x.IsActive == StatusCashbookTransaction.COMPLETED)
                    .GroupBy(x => 1)
                    .Select(g => new
                    {
                        TotalReceipts = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? x.Amount : 0),
                        TotalExpenditures = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? x.Amount : 0)
                    })
                    .FirstOrDefaultAsync();

                var yesterdayReceipts = yesterdayTotals?.TotalReceipts ?? 0;
                var yesterdayExpenditures = yesterdayTotals?.TotalExpenditures ?? 0;

                // === Tổng cùng kỳ tháng trước ===
                var previousMonthStart = startDate.AddMonths(-1);
                var previousMonthEnd = endDate.AddMonths(-1);

                var previousMonthTotals = await _dbContext.sm_Cashbook_Transaction
                    .Where(x => x.CreatedOnDate >= previousMonthStart && x.CreatedOnDate <= previousMonthEnd && x.IsActive == StatusCashbookTransaction.COMPLETED)
                    .GroupBy(x => 1)
                    .Select(g => new
                    {
                        TotalReceipts = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? x.Amount : 0),
                        TotalExpenditures = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? x.Amount : 0)
                    })
                    .FirstOrDefaultAsync();

                var prevMonthReceipts = previousMonthTotals?.TotalReceipts ?? 0;
                var prevMonthExpenditures = previousMonthTotals?.TotalExpenditures ?? 0;

                // === Tính phần trăm thay đổi ===
                decimal CalcPercentChange(decimal current, decimal previous)
                {
                    if (previous == 0) return current == 0 ? 0 : 100;
                    return Math.Round(((current - previous) / previous) * 100, 2);
                }

                var data = new
                {
                    TotalReceiptsAmount = totalReceipts / 1_000_000m,
                    TotalExpendituresAmount = totalExpenditures / 1_000_000m,
                    LeftAmount = (totalReceipts - totalExpenditures) / 1_000_000m,

                    PercentChangeReceipts_Yesterday = CalcPercentChange(totalReceipts, yesterdayReceipts),
                    PercentChangeExpenditures_Yesterday = CalcPercentChange(totalExpenditures, yesterdayExpenditures),

                    PercentChangeReceipts_LastMonth = CalcPercentChange(totalReceipts, prevMonthReceipts),
                    PercentChangeExpenditures_LastMonth = CalcPercentChange(totalExpenditures, prevMonthExpenditures)
                };

                return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
                {
                    DashboardData = data
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
            }
        }

        //public async Task<Response<CashbookDashboardViewModel>> DashboardWithTotalFilter(CashbookDashboardFilterDatetimeModel filter)
        //{
        //    try
        //    {
        //        var totals = await _dbContext.sm_Cashbook_Transaction
        //            .Where(x => x.CreatedOnDate >= filter.DateRange[0].Value.StartOfDay() && x.CreatedOnDate <= filter.DateRange[1].Value.EndOfDay() && x.IsActive == StatusCashbookTransaction.COMPLETED)
        //            .GroupBy(x => 1)
        //            .Select(g => new
        //            {
        //                TotalReceiptsAmount = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType ? x.Amount : 0),
        //                TotalExpendituresAmount = g.Sum(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType ? x.Amount : 0)
        //            })
        //            .FirstOrDefaultAsync();

        //        var totalReceiptsAmount = totals?.TotalReceiptsAmount ?? 0;
        //        var totalExpendituresAmount = totals?.TotalExpendituresAmount ?? 0;
        //        var data = new
        //        {
        //            TotalReceiptsAmount = totalReceiptsAmount / 1000000,
        //            TotalExpendituresAmount = totalExpendituresAmount / 1000000,
        //            LeftAmount = (totalReceiptsAmount - totalExpendituresAmount) / 1000000,
        //        };
        //        return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
        //        {
        //            DashboardData = data
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, string.Empty);
        //        return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
        //    }
        //}

        //public async Task<Response<CashbookDashboardViewModel>> ReceiptsDashboardFilter(CashbookDashboardFilterDatetimeModel filter)
        //{
        //    try
        //    {
        //        // Lấy startDate và endDate từ filter (người dùng tự chọn)
        //        var startDate = filter.DateRange[0].Value.StartOfDay(); // Chọn startDate từ giao diện người dùng
        //        var endDate = filter.DateRange[1].Value.EndOfDay(); // Chọn endDate từ giao diện người dùng

        //        // Lọc dữ liệu theo thời gian
        //        var transactions = await _dbContext.sm_Cashbook_Transaction
        //            .Where(x => x.CreatedOnDate >= startDate && x.CreatedOnDate <= endDate && x.IsActive == StatusCashbookTransaction.COMPLETED)
        //            .ToListAsync();

        //        // Lọc thêm theo TransactionType nếu có
        //        if (!string.IsNullOrEmpty(filter.TransactionType))
        //        {
        //            if (filter.TransactionType == CashbookTransactionConstants.ReceiptVoucherType) // Chỉ tính thu
        //            {
        //                transactions = transactions
        //                    .Where(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType)
        //                    .ToList();
        //            }
        //            else if (filter.TransactionType == CashbookTransactionConstants.PaymentVoucherType) // Chỉ tính chi
        //            {
        //                transactions = transactions
        //                    .Where(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType)
        //                    .ToList();
        //            }
        //        }

        //        // Khởi tạo mảng xaxis (theo các ngày trong khoảng thời gian)
        //        var xaxis = new List<string>();
        //        var yaxis = new List<string>();  // Mảng chứa số liệu thu hoặc chi

        //        // Lặp qua các ngày trong khoảng thời gian và tính tổng thu chi cho mỗi ngày
        //        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        //        {
        //            // Lọc các giao dịch trong ngày này
        //            var totalReceipts = transactions
        //                .Where(x => x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType && x.CreatedOnDate.Date == date.Date)
        //                .Sum(x => x.Amount);

        //            var totalExpenditures = transactions
        //                .Where(x => x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType && x.CreatedOnDate.Date == date.Date)
        //                .Sum(x => x.Amount);

        //            // Thêm giá trị vào xaxis và yaxis
        //            xaxis.Add(date.ToString("dd/MM/yyyy"));

        //            // Nếu TransactionType là "THU", chỉ tính thu, còn "CHI" thì tính chi
        //            if (filter.TransactionType == "THU")
        //            {
        //                yaxis.Add(Math.Round(totalReceipts / 1_000_000m, 3).ToString("0.000")); // Số liệu thu (làm tròn 3 chữ số)
        //            }
        //            else if (filter.TransactionType == "CHI")
        //            {
        //                yaxis.Add(Math.Round(totalExpenditures / 1_000_000m, 3).ToString("0.000")); // Số liệu chi (làm tròn 3 chữ số)
        //            }
        //            else
        //            {
        //                // Nếu không có TransactionType, tính cả thu và chi
        //                var netAmount = totalReceipts - totalExpenditures;
        //                yaxis.Add(Math.Round(netAmount / 1_000_000m, 3).ToString("0.000")); // Số liệu thu chi (làm tròn 3 chữ số)
        //            }
        //        }

        //        var data = new
        //        {
        //            xaxis = xaxis,
        //            yaxis = yaxis
        //        };

        //        return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
        //        {
        //            DashboardData = data
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, string.Empty);
        //        return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
        //    }
        //}

        public async Task<Response<CashbookDashboardViewModel>> ReceiptsDashboardFilter(CashbookDashboardFilterModel filter)
        {
            try
            {
                var dateNow = DateTime.Now;
                var startOfCurrentDay = dateNow.Date;
                var startOfCurrentMonth = new DateTime(dateNow.Year, dateNow.Month, 1);
                var startOfCurrentYear = new DateTime(dateNow.Year, 1, 1);
                var timeKindList = new List<object>();
                var amountList = new List<decimal>();

                switch (filter.DateType)
                {
                    case "DAYS":
                        var hoursCount = dateNow.Hour + 1;
                        for (var i = 0; i < hoursCount; i++)
                        {
                            timeKindList.Add(startOfCurrentDay.AddHours(i).Hour);
                        }
                        switch (filter.TransactionType)
                        {
                            case CashbookTransactionConstants.ReceiptVoucherType:
                                {
                                    foreach (var hour in timeKindList)
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddDays(-1).Date &&
                                                x.CreatedOnDate.Hour == (int)hour)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }

                                    break;
                                }
                            case CashbookTransactionConstants.PaymentVoucherType:
                                {
                                    foreach (var hour in timeKindList)
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddDays(-1).Date &&
                                                x.CreatedOnDate.Hour == (int)hour)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }

                                    break;
                                }
                        }
                        timeKindList = timeKindList.Select(object (hour) => Convert.ToString(hour + "h")).ToList();
                        break;
                    case "MONTHS":
                        var dayCount = dateNow.Day;
                        for (var i = 0; i < dayCount; i++)
                        {
                            timeKindList.Add(startOfCurrentMonth.AddDays(i).Date);
                        }
                        switch (filter.TransactionType)
                        {
                            case CashbookTransactionConstants.ReceiptVoucherType:
                                {
                                    foreach (var day in timeKindList)
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddMonths(-1).Date &&
                                                x.CreatedOnDate.Date == (DateTime)day)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }
                                    break;
                                }
                            case CashbookTransactionConstants.PaymentVoucherType:
                                {
                                    foreach (var day in timeKindList)
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddMonths(-1).Date &&
                                                x.CreatedOnDate.Date == (DateTime)day)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }
                                    break;
                                }
                        }
                        timeKindList = timeKindList
                            .Select(object (date) => Convert.ToDateTime(date).ToString("dd-MM-yyyy"))
                            .ToList();
                        break;
                    case "YEARS":
                        var monthCount = dateNow.Month;
                        for (var i = 0; i < monthCount; i++)
                        {
                            timeKindList.Add(startOfCurrentYear.AddMonths(i).Date.ToString("MM/yyyy"));
                        }


                        switch (filter.TransactionType)
                        {
                            case CashbookTransactionConstants.ReceiptVoucherType:
                                {
                                    foreach (var typeMonthYear in timeKindList.Select(month =>
                                                 DateTime.ParseExact((string)month, "MM/yyyy", null)))
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.ReceiptVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddYears(-1).Date &&
                                                x.CreatedOnDate.Year == typeMonthYear.Year &&
                                                x.CreatedOnDate.Month == typeMonthYear.Month)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }

                                    break;
                                }
                            case CashbookTransactionConstants.PaymentVoucherType:
                                {
                                    foreach (var typeMonthYear in timeKindList.Select(month =>
                                                 DateTime.ParseExact((string)month, "MM/yyyy", null)))
                                    {
                                        var totalAmount = await _dbContext.sm_Cashbook_Transaction.Where(x =>
                                                x.TransactionTypeCode == CashbookTransactionConstants.PaymentVoucherType &&
                                                x.CreatedOnDate.Date >= DateTime.Now.AddYears(-1).Date &&
                                                x.CreatedOnDate.Year == typeMonthYear.Year &&
                                                x.CreatedOnDate.Month == typeMonthYear.Month)
                                            .SumAsync(x => x.Amount);
                                        amountList.Add(totalAmount);
                                    }

                                    break;
                                }
                        }
                        break;
                }

                var data = new
                {
                    Xaxis = timeKindList,
                    Yaxis = amountList.Select(x => (x / 1000000).ToString("0.000"))
                };
                return new Response<CashbookDashboardViewModel>(new CashbookDashboardViewModel()
                {
                    DashboardData = data
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<CashbookDashboardViewModel>(ex);
            }
        }

        public async Task<Response<string>> ExportExcelCashFlowReportAsync(CashbookTransactionQueryModel query)
        {
            try
            {
                // Chặn Chống Spam Xuất Excel (tối thiểu sau 20s mới được xuất lần nữa)
                var lastExportTime = _cache.Get<DateTime?>("LastExportTime");
                if (lastExportTime.HasValue && DateTime.Now.Subtract(lastExportTime.Value).TotalSeconds < 20)
                    return Helper.CreateBadRequestResponse<string>("Vui lòng chờ ít nhất 20 giây trước khi xuất file Excel tiếp theo");

                // Lưu thời gian hiện tại vào cache để lần sau kiểm tra
                _cache.Set("LastExportTime", DateTime.Now, TimeSpan.FromSeconds(20));

                var cashFlow = await GetCashFlowReportAsync(query);
                var cashFlowReport = cashFlow.Data;

                // Đặt tên file và đường dẫn template dựa trên loại phiếu
                var fileName = $"báo cáo dòng tiền năm_{query.YearDate.Year}_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx";
                var filePath = Path.Combine(_staticsFolder, fileName);

                // Xác định đường dẫn template dựa trên loại phiếu
                var templatePath = Path.Combine(_staticsFolder, "excel-template/CashFlowReportTemplate.xlsx");
                //var templatePath = "C:/opt/geneat/file/excel-template/CashFlowReportTemplate.xlsx";

                if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                    return Helper.CreateBadRequestResponse<string>("Không tìm thấy file template");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(new FileInfo(templatePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    var row = 6;

                    // Year
                    worksheet.Cells[5, 2].Value = query.YearDate.Year;
                    worksheet.Cells[5, 2].Style.Font.Bold = true;

                    // opencashbalance
                    worksheet.Cells[row, 2].Value = cashFlowReport.OpeningCashBalance.Title;
                    worksheet.Cells[row, 3].Value = cashFlowReport.OpeningCashBalance.StartAmount;
                    worksheet.Cells[row, 4].Value = cashFlowReport.OpeningCashBalance.JanuaryAmount;
                    worksheet.Cells[row, 5].Value = cashFlowReport.OpeningCashBalance.FebruaryAmount;
                    worksheet.Cells[row, 6].Value = cashFlowReport.OpeningCashBalance.MarchAmount;
                    worksheet.Cells[row, 7].Value = cashFlowReport.OpeningCashBalance.AprilAmount;
                    worksheet.Cells[row, 8].Value = cashFlowReport.OpeningCashBalance.MayAmount;
                    worksheet.Cells[row, 9].Value = cashFlowReport.OpeningCashBalance.JuneAmount;
                    worksheet.Cells[row, 10].Value = cashFlowReport.OpeningCashBalance.JulyAmount;
                    worksheet.Cells[row, 11].Value = cashFlowReport.OpeningCashBalance.AugustAmount;
                    worksheet.Cells[row, 12].Value = cashFlowReport.OpeningCashBalance.SeptemberAmount;
                    worksheet.Cells[row, 13].Value = cashFlowReport.OpeningCashBalance.OctoberAmount;
                    worksheet.Cells[row, 14].Value = cashFlowReport.OpeningCashBalance.NovemberAmount;
                    worksheet.Cells[row, 15].Value = cashFlowReport.OpeningCashBalance.DecemberAmount;
                    worksheet.Cells[row, 16].Value = cashFlowReport.OpeningCashBalance.TotalAmount;

                    row += 2;

                    // receiptVouchers items
                    foreach (var item in cashFlowReport.ReceiptVouchers.Items)
                    {
                        worksheet.Row(row).Height = 33.5;
                        worksheet.Cells[row, 1].Value = item.LineNumber;
                        worksheet.Cells[row, 2].Value = item.Title;
                        worksheet.Cells[row, 4].Value = item.JanuaryAmount;
                        worksheet.Cells[row, 5].Value = item.FebruaryAmount;
                        worksheet.Cells[row, 6].Value = item.MarchAmount;
                        worksheet.Cells[row, 7].Value = item.AprilAmount;
                        worksheet.Cells[row, 8].Value = item.MayAmount;
                        worksheet.Cells[row, 9].Value = item.JuneAmount;
                        worksheet.Cells[row, 10].Value = item.JulyAmount;
                        worksheet.Cells[row, 11].Value = item.AugustAmount;
                        worksheet.Cells[row, 12].Value = item.SeptemberAmount;
                        worksheet.Cells[row, 13].Value = item.OctoberAmount;
                        worksheet.Cells[row, 14].Value = item.NovemberAmount;
                        worksheet.Cells[row, 15].Value = item.DecemberAmount;
                        worksheet.Cells[row, 16].Value = item.TotalAmount;
                        row++;
                    }

                    // receiptVouchers total
                    worksheet.Row(row).Height = 33.5;
                    worksheet.Cells[row, 2].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.Title;
                    worksheet.Cells[row, 4].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.JanuaryAmount;
                    worksheet.Cells[row, 5].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.FebruaryAmount;
                    worksheet.Cells[row, 6].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.MarchAmount;
                    worksheet.Cells[row, 7].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.AprilAmount;
                    worksheet.Cells[row, 8].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.MayAmount;
                    worksheet.Cells[row, 9].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.JuneAmount;
                    worksheet.Cells[row, 10].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.JulyAmount;
                    worksheet.Cells[row, 11].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.AugustAmount;
                    worksheet.Cells[row, 12].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.SeptemberAmount;
                    worksheet.Cells[row, 13].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.OctoberAmount;
                    worksheet.Cells[row, 14].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.NovemberAmount;
                    worksheet.Cells[row, 15].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.DecemberAmount;
                    worksheet.Cells[row, 16].Value = cashFlowReport.ReceiptVouchers.ReceiptTotalAmount.TotalAmount;
                    // màu nền cho total từ cột 1 đầu đến cột 16
                    worksheet.Cells[row, 1, row, 16].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 1, row, 16].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(166, 201, 236));
                    worksheet.Cells[row, 1, row, 16].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 16].Style.Font.Color.SetColor(Color.Black);


                    // receiptVouchers total cash balance
                    row += 1;
                    worksheet.Row(row).Height = 33.5;
                    worksheet.Cells[row, 2].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.Title;
                    worksheet.Cells[row, 4].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.JanuaryAmount;
                    worksheet.Cells[row, 5].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.FebruaryAmount;
                    worksheet.Cells[row, 6].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.MarchAmount;
                    worksheet.Cells[row, 7].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.AprilAmount;
                    worksheet.Cells[row, 8].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.MayAmount;
                    worksheet.Cells[row, 9].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.JuneAmount;
                    worksheet.Cells[row, 10].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.JulyAmount;
                    worksheet.Cells[row, 11].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.AugustAmount;
                    worksheet.Cells[row, 12].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.SeptemberAmount;
                    worksheet.Cells[row, 13].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.OctoberAmount;
                    worksheet.Cells[row, 14].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.NovemberAmount;
                    worksheet.Cells[row, 15].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.DecemberAmount;
                    worksheet.Cells[row, 16].Value = cashFlowReport.ReceiptVouchers.TotalCashBalance.TotalAmount;
                    // màu nền cho total từ cột 1 đầu đến cột 16
                    worksheet.Cells[row, 1, row, 16].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 1, row, 16].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(166, 201, 236));
                    worksheet.Cells[row, 1, row, 16].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 16].Style.Font.Color.SetColor(Color.Black);

                    row += 1;
                    worksheet.Row(row).Height = 33.5;
                    worksheet.Cells[row, 1].Value = "#";
                    worksheet.Cells[row, 2].Value = "Dòng tiền chi ra";
                    worksheet.Cells[row, 1, row, 16].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 2, row, 16].Merge = true;
                    worksheet.Cells[row, 2, row, 16].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 16].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 32, 96));
                    worksheet.Cells[row, 1, row, 16].Style.Font.Color.SetColor(Color.White);

                    row += 1;
                    // paymentVouchers items
                    foreach (var item in cashFlowReport.PaymentVouchers.Items)
                    {
                        worksheet.Row(row).Height = 33.5;
                        worksheet.Cells[row, 1].Value = item.LineNumber;
                        worksheet.Cells[row, 2].Value = item.Title;
                        worksheet.Cells[row, 4].Value = item.JanuaryAmount;
                        worksheet.Cells[row, 5].Value = item.FebruaryAmount;
                        worksheet.Cells[row, 6].Value = item.MarchAmount;
                        worksheet.Cells[row, 7].Value = item.AprilAmount;
                        worksheet.Cells[row, 8].Value = item.MayAmount;
                        worksheet.Cells[row, 9].Value = item.JuneAmount;
                        worksheet.Cells[row, 10].Value = item.JulyAmount;
                        worksheet.Cells[row, 11].Value = item.AugustAmount;
                        worksheet.Cells[row, 12].Value = item.SeptemberAmount;
                        worksheet.Cells[row, 13].Value = item.OctoberAmount;
                        worksheet.Cells[row, 14].Value = item.NovemberAmount;
                        worksheet.Cells[row, 15].Value = item.DecemberAmount;
                        worksheet.Cells[row, 16].Value = item.TotalAmount;
                        row++;
                    }

                    // paymentVouchers total
                    worksheet.Row(row).Height = 33.5;
                    worksheet.Cells[row, 2].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.Title;
                    worksheet.Cells[row, 4].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.JanuaryAmount;
                    worksheet.Cells[row, 5].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.FebruaryAmount;
                    worksheet.Cells[row, 6].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.MarchAmount;
                    worksheet.Cells[row, 7].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.AprilAmount;
                    worksheet.Cells[row, 8].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.MayAmount;
                    worksheet.Cells[row, 9].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.JuneAmount;
                    worksheet.Cells[row, 10].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.JulyAmount;
                    worksheet.Cells[row, 11].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.AugustAmount;
                    worksheet.Cells[row, 12].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.SeptemberAmount;
                    worksheet.Cells[row, 13].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.OctoberAmount;
                    worksheet.Cells[row, 14].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.NovemberAmount;
                    worksheet.Cells[row, 15].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.DecemberAmount;
                    worksheet.Cells[row, 16].Value = cashFlowReport.PaymentVouchers.PaymentTotalAmount.TotalAmount;
                    // màu nền cho total từ cột 1 đầu đến cột 16
                    worksheet.Cells[row, 1, row, 16].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 1, row, 16].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(166, 201, 236));
                    worksheet.Cells[row, 1, row, 16].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 16].Style.Font.Color.SetColor(Color.Black);

                    // paymentVouchers total cash balance
                    row += 1;
                    worksheet.Row(row).Height = 33.5;
                    worksheet.Cells[row, 2].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.Title;
                    worksheet.Cells[row, 4].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.JanuaryAmount;
                    worksheet.Cells[row, 5].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.FebruaryAmount;
                    worksheet.Cells[row, 6].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.MarchAmount;
                    worksheet.Cells[row, 7].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.AprilAmount;
                    worksheet.Cells[row, 8].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.MayAmount;
                    worksheet.Cells[row, 9].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.JuneAmount;
                    worksheet.Cells[row, 10].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.JulyAmount;
                    worksheet.Cells[row, 11].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.AugustAmount;
                    worksheet.Cells[row, 12].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.SeptemberAmount;
                    worksheet.Cells[row, 13].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.OctoberAmount;
                    worksheet.Cells[row, 14].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.NovemberAmount;
                    worksheet.Cells[row, 15].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.DecemberAmount;
                    worksheet.Cells[row, 16].Value = cashFlowReport.PaymentVouchers.TotalCashBalance.TotalAmount;// màu nền cho total từ cột 1 đầu đến cột 16
                    worksheet.Cells[row, 1, row, 16].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 1, row, 16].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(166, 201, 236));
                    worksheet.Cells[row, 1, row, 16].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 16].Style.Font.Color.SetColor(Color.Black);

                    int lastDataRow = row - 1;

                    // Tự động điều chỉnh kích thước các cột từ cột thứ hai đến cột cuối cùng theo value có ký tự nhiều nhất
                    worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column].AutoFitColumns();

                    // Lưu file Excel đã điền dữ liệu
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                return Helper.CreateSuccessResponse<string>(filePath, "Xuất file thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<string>(ex);
            }
        }
    }
}
