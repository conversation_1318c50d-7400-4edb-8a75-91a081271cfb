CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

CREATE TABLE "bsd_Key_Value" (
    "Key" character varying(128) NOT NULL,
    "Value" text NULL,
    CONSTRAINT "PK_bsd_Key_Value" PRIMARY KEY ("Key")
);

CREATE TABLE "bsd_Navigation" (
    "Id" uuid NOT NULL,
    "ParentId" uuid NULL,
    "Code" character varying(64) NOT NULL,
    "Name" character varying(128) NOT NULL,
    "UrlRewrite" character varying(128) NULL,
    "IdPath" character varying(450) NOT NULL,
    "Path" character varying(900) NOT NULL,
    "SubUrl" character varying(1024) NULL,
    "IconClass" character varying(64) NULL,
    "Status" boolean NULL,
    "Order" integer NULL,
    "HasChild" boolean NOT NULL,
    "Level" integer NOT NULL,
    "Type" integer NULL,
    "QueryParams" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NOT NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "ApplicationId" uuid NOT NULL,
    CONSTRAINT "PK_bsd_Navigation" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_bsd_Navigation_bsd_Navigation_ParentId" FOREIGN KEY ("ParentId") REFERENCES "bsd_Navigation" ("Id") ON DELETE CASCADE
);

CREATE TABLE "bsd_Parameter" (
    "Id" uuid NOT NULL,
    "Name" character varying(64) NOT NULL,
    "Description" character varying(1024) NULL,
    "Value" text NULL,
    "IsSystem" boolean NOT NULL,
    "GroupCode" character varying(64) NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NOT NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "ApplicationId" uuid NOT NULL,
    CONSTRAINT "PK_bsd_Parameter" PRIMARY KEY ("Id")
);

CREATE TABLE "cata_Commune" (
    "CommuneCode" integer GENERATED BY DEFAULT AS IDENTITY,
    "CommuneName" text NULL,
    "DistrictCode" integer NOT NULL,
    "WardId_VP" integer NOT NULL,
    "WardName_VP" text NULL,
    "DistrictId_VP" integer NOT NULL,
    "VNPAsciiName" text NULL,
    "VTPAsciiName" text NULL,
    "OldVTPName" text NULL,
    "VnpostSyncStatus" integer NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_cata_Commune" PRIMARY KEY ("CommuneCode")
);

CREATE TABLE "cata_District" (
    "DistrictCode" integer GENERATED BY DEFAULT AS IDENTITY,
    "DistrictName" text NULL,
    "Description" text NULL,
    "ProvinceCode" integer NOT NULL,
    "DistrictShippingType" integer NOT NULL,
    "DistrictId_VP" integer NOT NULL,
    "DistrictValue_VP" text NULL,
    "DistrictName_VP" text NULL,
    "ProvinceId_VP" integer NOT NULL,
    "DelayShipFromDate" timestamp without time zone NULL,
    "StopShipFromDate" timestamp without time zone NULL,
    "DelayShipToDate" timestamp without time zone NULL,
    "StopShipToDate" timestamp without time zone NULL,
    "VnpostSyncStatus" integer NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_cata_District" PRIMARY KEY ("DistrictCode")
);

CREATE TABLE "cata_Province" (
    "ProvinceCode" integer GENERATED BY DEFAULT AS IDENTITY,
    "ProvinceName" text NULL,
    "Order" integer NOT NULL,
    "ProvinceShippingType" integer NOT NULL,
    "ProvinceId_VP" integer NOT NULL,
    "ProvinceCode_VP" text NULL,
    "ProvinceName_VP" text NULL,
    "ServiceType_VP" integer NOT NULL,
    "TotalDistrictShipDelay" integer NOT NULL,
    "TotalDistrictShipStop" integer NOT NULL,
    "DelayShipFromDate" timestamp without time zone NULL,
    "StopShipFromDate" timestamp without time zone NULL,
    "DelayShipToDate" timestamp without time zone NULL,
    "StopShipToDate" timestamp without time zone NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_cata_Province" PRIMARY KEY ("ProvinceCode")
);

CREATE TABLE "erp_Attachment" (
    "Id" uuid NOT NULL,
    "DocType" text NULL,
    "DocTypeName" text NULL,
    "EntityId" uuid NULL,
    "EntityType" character varying(15) NULL,
    "FilePath" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "IsDelete" boolean NOT NULL,
    "DeletedOnDate" timestamp without time zone NULL,
    "FileType" text NULL,
    "Description" text NULL,
    "Note" text NULL,
    "FileSize" bigint NOT NULL,
    "OriginalFileName" character varying(256) NULL,
    "StatusCode" character varying(15) NULL,
    "UpdateFrequency" text NULL,
    "Language" text NULL,
    "CopyRight" text NULL,
    "IsPrivate" boolean NOT NULL,
    "License" text NULL,
    "OrganizationId" uuid NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_erp_Attachment" PRIMARY KEY ("Id")
);

CREATE TABLE "idm_Role" (
    "Id" uuid NOT NULL,
    "Name" character varying(128) NOT NULL,
    "Code" character varying(64) NOT NULL,
    "Description" character varying(1024) NULL,
    "IsSystem" boolean NOT NULL,
    "Level" integer NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_idm_Role" PRIMARY KEY ("Id")
);

CREATE TABLE "idm_User" (
    "Id" uuid NOT NULL,
    "UserName" character varying(128) NOT NULL,
    "Name" character varying(128) NOT NULL,
    "PhoneNumber" character varying(32) NULL,
    "CountryCode" text NULL,
    "Currency" text NULL,
    "Language" text NULL,
    "Gender" text NULL,
    "Email" character varying(256) NULL,
    "AvatarUrl" character varying(1024) NULL,
    "Password" character varying(1024) NULL,
    "PasswordSalt" character varying(1024) NULL,
    "Birthdate" timestamp without time zone NULL,
    "LastActivityDate" timestamp without time zone NOT NULL,
    "PlainTextPwd" character varying(128) NULL,
    "IsLockedOut" boolean NOT NULL,
    "IsActive" boolean NOT NULL,
    "ActiveDate" timestamp without time zone NULL,
    "Level" integer NOT NULL,
    "UpdateLog" text NULL,
    "FacebookUserId" character varying(128) NULL,
    "GoogleUserId" character varying(128) NULL,
    "ResetPasswordToken" character varying(30) NULL,
    "EmailVerifyToken" character varying(30) NULL,
    "IsEmailVerified" boolean NOT NULL,
    "RoleListCode" text[] NULL,
    "BankAccountNo" text NULL,
    "BankName" text NULL,
    "BankUsername" text NULL,
    "StatusCode" text NULL,
    "DeviceToken" text NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_idm_User" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_PhongBan" (
    "Id" uuid NOT NULL,
    "MaPhongBan" text NOT NULL,
    "TenPhongBan" text NOT NULL,
    "GhiChu" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_PhongBan" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_ActiviyHisroty" (
    "Id" uuid NOT NULL,
    "EntityId" uuid NOT NULL,
    "EntityType" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_ActiviyHisroty" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_CodeType" (
    "Id" uuid NOT NULL,
    "ParentId" uuid NULL,
    "Title" text NULL,
    "Code" text NULL,
    "Order" integer NOT NULL,
    "Description" text NULL,
    "Type" text NULL,
    "TranslationCount" integer NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_CodeType" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_Email_Subscribe" (
    "Id" uuid NOT NULL,
    "Email" text NULL,
    "SubscribeDate" timestamp without time zone NULL,
    "UnsubscribeDate" timestamp without time zone NULL,
    "Status" character varying(20) NULL,
    "TotalEmailSentCount" integer NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Email_Subscribe" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_Email_Template" (
    "Id" uuid NOT NULL,
    "Name" character varying(128) NULL,
    "Code" character varying(64) NULL,
    "Description" character varying(256) NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Email_Template" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_Email_Verification" (
    "Id" uuid NOT NULL,
    "AccountId" uuid NOT NULL,
    "Email" text NULL,
    "Verified" boolean NOT NULL,
    "ValidDate" timestamp without time zone NULL,
    "VerifiedDate" timestamp without time zone NULL,
    "VerifyToken" text NULL,
    "Status" text NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Email_Verification" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_Notification_Template" (
    "Id" uuid NOT NULL,
    "Name" text NULL,
    "Type" text NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Notification_Template" PRIMARY KEY ("Id")
);

CREATE TABLE "bsd_Navigation_Map_Role" (
    "Id" bigint GENERATED BY DEFAULT AS IDENTITY,
    "NavigationId" uuid NOT NULL,
    "RoleId" uuid NOT NULL,
    "FromSubNavigation" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_bsd_Navigation_Map_Role" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_bsd_Navigation_Map_Role_bsd_Navigation_NavigationId" FOREIGN KEY ("NavigationId") REFERENCES "bsd_Navigation" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_KhachHang" (
    "Id" uuid NOT NULL,
    "Ma" text NULL,
    "Ten" text NULL,
    "GhiChu" text NULL,
    "SoDienThoai" text NULL,
    "DiaChi" text NULL,
    "Birthdate" timestamp without time zone NULL,
    "LinkFacebook" text NULL,
    "LinkTiktok" text NULL,
    "LinkTelegram" text NULL,
    "LoaiKhachHang" text[] NULL,
    "TrangThai" text NULL,
    "LastCareOnDate" timestamp without time zone NULL,
    "TotalCareTimes" integer NOT NULL,
    "NhuCauBanDau" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_KhachHang" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_KhachHang_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_Notification" (
    "Id" bigint GENERATED BY DEFAULT AS IDENTITY,
    "ReceiverUserId" uuid NULL,
    "IsReceiverRead" boolean NOT NULL,
    "IsReceiverSeen" boolean NOT NULL,
    "ReceiverReadOnDate" timestamp without time zone NULL,
    "Type" text NULL,
    "CreatedByUserId" uuid NULL,
    "JsonData" text NULL,
    "JsonDataType" text NULL,
    "Content" text NULL,
    "Title" text NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Notification" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Notification_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Notification_idm_User_ReceiverUserId" FOREIGN KEY ("ReceiverUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_CodeType_Translation" (
    "Id" uuid NOT NULL,
    "Title" text NULL,
    "Description" text NULL,
    "Language" text NULL,
    "Type" text NULL,
    "CodeTypeId" uuid NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_CodeType_Translation" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_CodeType_Translation_sm_CodeType_CodeTypeId" FOREIGN KEY ("CodeTypeId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_Email_Template_Translations" (
    "Id" uuid NOT NULL,
    "EmailTemplateId" uuid NOT NULL,
    "TitleTemplate" character varying(512) NULL,
    "BodyTemplate" text NULL,
    "Language" character varying(3) NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Email_Template_Translations" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Email_Template_Translations_sm_Email_Template_EmailTempl~" FOREIGN KEY ("EmailTemplateId") REFERENCES "sm_Email_Template" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_Notification_Template_Translation" (
    "Id" uuid NOT NULL,
    "NotificationTemplateId" uuid NOT NULL,
    "TitleTemplate" character varying(512) NULL,
    "BodyPlainTextTemplate" text NULL,
    "BodyHtmlTemplate" text NULL,
    "Language" character varying(3) NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_sm_Notification_Template_Translation" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Notification_Template_Translation_sm_Notification_Templa~" FOREIGN KEY ("NotificationTemplateId") REFERENCES "sm_Notification_Template" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_LichSuChamSoc" (
    "Id" uuid NOT NULL,
    "GhiChu" text NULL,
    "DanhGia" integer NOT NULL,
    "KhachHangId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_LichSuChamSoc" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_LichSuChamSoc_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_LichSuChamSoc_sm_KhachHang_KhachHangId" FOREIGN KEY ("KhachHangId") REFERENCES "sm_KhachHang" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_bsd_Navigation_ParentId" ON "bsd_Navigation" ("ParentId");

CREATE INDEX "IX_bsd_Navigation_Map_Role_NavigationId" ON "bsd_Navigation_Map_Role" ("NavigationId");

CREATE INDEX "IX_sm_CodeType_Translation_CodeTypeId" ON "sm_CodeType_Translation" ("CodeTypeId");

CREATE INDEX "IX_sm_Email_Template_Translations_EmailTemplateId" ON "sm_Email_Template_Translations" ("EmailTemplateId");

CREATE INDEX "IX_sm_KhachHang_CreatedByUserId" ON "sm_KhachHang" ("CreatedByUserId");

CREATE INDEX "IX_sm_LichSuChamSoc_CreatedByUserId" ON "sm_LichSuChamSoc" ("CreatedByUserId");

CREATE INDEX "IX_sm_LichSuChamSoc_KhachHangId" ON "sm_LichSuChamSoc" ("KhachHangId");

CREATE INDEX "IX_sm_Notification_CreatedByUserId" ON "sm_Notification" ("CreatedByUserId");

CREATE INDEX "IX_sm_Notification_ReceiverUserId" ON "sm_Notification" ("ReceiverUserId");

CREATE INDEX "IX_sm_Notification_Template_Translation_NotificationTemplateId" ON "sm_Notification_Template_Translation" ("NotificationTemplateId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240601044159_init', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_ChucVu" (
    "Id" uuid NOT NULL,
    "MaChucVu" text NOT NULL,
    "TenChucVu" text NOT NULL,
    "GhiChu" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_ChucVu" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240601081938_add-api-chucvu', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_NhomVatTu" (
    "Id" uuid NOT NULL,
    "TenNhom" text NOT NULL,
    "GhiChu" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_NhomVatTu" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240603021133_add-api-nhomvattu', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_DuAn" (
    "Id" uuid NOT NULL,
    "MaDuAn" text NULL,
    "TenDuAn" text NULL,
    "TongHopThu" text NULL,
    "TongHopChi" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_DuAn" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_NhaCungCap" (
    "Id" uuid NOT NULL,
    "MaNhaCungCap" text NULL,
    "TenNhaCungCap" text NULL,
    "DiaChi" text NULL,
    "SoDienThoai" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_NhaCungCap" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_SanPham" (
    "Id" uuid NOT NULL,
    "MaSanPham" text NOT NULL,
    "TenSanPham" text NULL,
    "DonViTinh" text NULL,
    "DonGia" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_SanPham" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_VatTu" (
    "Id" uuid NOT NULL,
    "MaVatTu" text NULL,
    "TenVatTu" text NULL,
    "DonViTinh" text NULL,
    "MoTa" text NULL,
    "HinhDaiDien" text NULL,
    "HinhDinhKem" text NULL,
    "GhiChu" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_VatTu" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_BaoGia" (
    "Id" uuid NOT NULL,
    "IdKhachHang" uuid NOT NULL,
    "TongTien" text NULL,
    "IdDuAn" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_BaoGia" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_BaoGia_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_BaoGia_sm_KhachHang_IdKhachHang" FOREIGN KEY ("IdKhachHang") REFERENCES "sm_KhachHang" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_ChiPhi" (
    "Id" uuid NOT NULL,
    "ChiPhiType" text NULL,
    "MaChi" text NULL,
    "IdMaNhaCungCap" uuid NOT NULL,
    "ChiChoMucDich" text NULL,
    "IdKhachHang" uuid NOT NULL,
    "SoTien" text NULL,
    "GhiChu" text NULL,
    "IdDuAn" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_ChiPhi" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_ChiPhi_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_ChiPhi_mk_NhaCungCap_IdMaNhaCungCap" FOREIGN KEY ("IdMaNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_ChiPhi_sm_KhachHang_IdKhachHang" FOREIGN KEY ("IdKhachHang") REFERENCES "sm_KhachHang" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_Bom" (
    "Id" uuid NOT NULL,
    "MaBom" text NULL,
    "IdSanPham" uuid NOT NULL,
    "mk_SanPhamId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_Bom" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_Bom_mk_SanPham_mk_SanPhamId" FOREIGN KEY ("mk_SanPhamId") REFERENCES "mk_SanPham" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_QuanLyKho" (
    "Id" uuid NOT NULL,
    "MaKho" text NULL,
    "MaDonHang" text NULL,
    "MucDichNhapKho" text NULL,
    "IdVatTu" uuid NOT NULL,
    "SoLuong" text NULL,
    "GiaTien" text NULL,
    "TongTien" text NULL,
    "LoaiPhieu" text NULL,
    "IdDuAn" uuid NOT NULL,
    "IdNhaCungCap" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_QuanLyKho" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_QuanLyKho_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_QuanLyKho_mk_NhaCungCap_IdNhaCungCap" FOREIGN KEY ("IdNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_QuanLyKho_mk_VatTu_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_BaoGiaItem" (
    "Id" uuid NOT NULL,
    "BaoGiaType" text NULL,
    "IdVatTu" uuid NOT NULL,
    "IdSanPham" uuid NOT NULL,
    "DonGia" text NULL,
    "DonVi" text NULL,
    "SoLuong" text NULL,
    "TongTien" text NULL,
    "IdBaoGia" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_BaoGiaItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_BaoGiaItem_mk_BaoGia_IdBaoGia" FOREIGN KEY ("IdBaoGia") REFERENCES "mk_BaoGia" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_BaoGiaItem_mk_SanPham_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "mk_SanPham" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_BaoGiaItem_mk_VatTu_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_NguyenVatLieu" (
    "Id" uuid NOT NULL,
    "TenNguyenVatLieu" text NOT NULL,
    "MaNguyenVatLieu" text NULL,
    "DonViTinh" text NULL,
    "SoLuong" text NULL,
    "DonGia" text NULL,
    "IdBom" uuid NOT NULL,
    "IdVatTu" uuid NOT NULL,
    "IdSanPham" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_NguyenVatLieu" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_NguyenVatLieu_mk_Bom_IdBom" FOREIGN KEY ("IdBom") REFERENCES "mk_Bom" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_NguyenVatLieu_mk_Bom_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "mk_Bom" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_NguyenVatLieu_mk_VatTu_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_BaoGia_IdDuAn" ON "mk_BaoGia" ("IdDuAn");

CREATE INDEX "IX_mk_BaoGia_IdKhachHang" ON "mk_BaoGia" ("IdKhachHang");

CREATE INDEX "IX_mk_BaoGiaItem_IdBaoGia" ON "mk_BaoGiaItem" ("IdBaoGia");

CREATE INDEX "IX_mk_BaoGiaItem_IdSanPham" ON "mk_BaoGiaItem" ("IdSanPham");

CREATE INDEX "IX_mk_BaoGiaItem_IdVatTu" ON "mk_BaoGiaItem" ("IdVatTu");

CREATE INDEX "IX_mk_Bom_mk_SanPhamId" ON "mk_Bom" ("mk_SanPhamId");

CREATE INDEX "IX_mk_ChiPhi_IdDuAn" ON "mk_ChiPhi" ("IdDuAn");

CREATE INDEX "IX_mk_ChiPhi_IdKhachHang" ON "mk_ChiPhi" ("IdKhachHang");

CREATE INDEX "IX_mk_ChiPhi_IdMaNhaCungCap" ON "mk_ChiPhi" ("IdMaNhaCungCap");

CREATE INDEX "IX_mk_NguyenVatLieu_IdBom" ON "mk_NguyenVatLieu" ("IdBom");

CREATE INDEX "IX_mk_NguyenVatLieu_IdSanPham" ON "mk_NguyenVatLieu" ("IdSanPham");

CREATE INDEX "IX_mk_NguyenVatLieu_IdVatTu" ON "mk_NguyenVatLieu" ("IdVatTu");

CREATE INDEX "IX_mk_QuanLyKho_IdDuAn" ON "mk_QuanLyKho" ("IdDuAn");

CREATE INDEX "IX_mk_QuanLyKho_IdNhaCungCap" ON "mk_QuanLyKho" ("IdNhaCungCap");

CREATE INDEX "IX_mk_QuanLyKho_IdVatTu" ON "mk_QuanLyKho" ("IdVatTu");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240603091750_add-api-sanpham-bom', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_NguyenVatLieu" ALTER COLUMN "TenNguyenVatLieu" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240604014420_fix-require', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_Bom" DROP CONSTRAINT "FK_mk_Bom_mk_SanPham_mk_SanPhamId";

ALTER TABLE "mk_NguyenVatLieu" DROP CONSTRAINT "FK_mk_NguyenVatLieu_mk_Bom_IdSanPham";

DROP INDEX "IX_mk_NguyenVatLieu_IdSanPham";

DROP INDEX "IX_mk_Bom_mk_SanPhamId";

ALTER TABLE "mk_NguyenVatLieu" DROP COLUMN "IdSanPham";

ALTER TABLE "mk_Bom" DROP COLUMN "mk_SanPhamId";

CREATE INDEX "IX_mk_Bom_IdSanPham" ON "mk_Bom" ("IdSanPham");

ALTER TABLE "mk_Bom" ADD CONSTRAINT "FK_mk_Bom_mk_SanPham_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "mk_SanPham" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240604021514_fix-public-vỉtual', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" DROP COLUMN "HinhDaiDien";

ALTER TABLE "mk_VatTu" DROP COLUMN "HinhDinhKem";

ALTER TABLE "mk_VatTu" ADD "Attachments" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240605081926_add-field-Attachments', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" ADD "IdNhomVatTu" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "mk_NhomVatTu" ALTER COLUMN "TenNhom" DROP NOT NULL;

ALTER TABLE "mk_NhomVatTu" ADD "MaNhom" text NOT NULL DEFAULT '';

CREATE INDEX "IX_mk_VatTu_IdNhomVatTu" ON "mk_VatTu" ("IdNhomVatTu");

ALTER TABLE "mk_VatTu" ADD CONSTRAINT "FK_mk_VatTu_mk_NhomVatTu_IdNhomVatTu" FOREIGN KEY ("IdNhomVatTu") REFERENCES "mk_NhomVatTu" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240605090728_update-fk-IDNhomVatTu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_NhaCungCap" ADD "GhiChu" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240606080624_add-filed-ghichu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_Bom" DROP COLUMN "MaBom";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240611014127_delete-field-maBom', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_SanPham" ALTER COLUMN "DonGia" TYPE numeric;

ALTER TABLE "mk_QuanLyKho" ALTER COLUMN "TongTien" TYPE numeric;

ALTER TABLE "mk_QuanLyKho" ALTER COLUMN "SoLuong" TYPE integer;

ALTER TABLE "mk_QuanLyKho" ALTER COLUMN "GiaTien" TYPE numeric;

ALTER TABLE "mk_NguyenVatLieu" ALTER COLUMN "SoLuong" TYPE integer;

ALTER TABLE "mk_NguyenVatLieu" ALTER COLUMN "DonGia" TYPE numeric;

ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopThu" TYPE numeric;

ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopChi" TYPE numeric;

ALTER TABLE "mk_ChiPhi" ALTER COLUMN "SoTien" TYPE numeric;

ALTER TABLE "mk_BaoGiaItem" ALTER COLUMN "TongTien" TYPE numeric;

ALTER TABLE "mk_BaoGiaItem" ALTER COLUMN "SoLuong" TYPE integer;

ALTER TABLE "mk_BaoGiaItem" ALTER COLUMN "DonGia" TYPE numeric;

ALTER TABLE "mk_BaoGia" ALTER COLUMN "TongTien" TYPE numeric;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240614072938_edit-type-decimal', '6.0.27');

COMMIT;

START TRANSACTION;

DROP TABLE "mk_QuanLyKho";

ALTER TABLE "mk_VatTu" ADD "IdVatTu" uuid NULL;

CREATE TABLE "mk_PhieuNhapKho" (
    "Id" uuid NOT NULL,
    "MaKho" text NULL,
    "MaDonHang" text NULL,
    "MucDichNhapKho" text NULL,
    "IdVatTu" uuid NOT NULL,
    "SoLuong" integer NULL,
    "GiaTien" numeric NULL,
    "TongTien" numeric NULL,
    "IdDuAn" uuid NOT NULL,
    "IdNhaCungCap" uuid NOT NULL,
    "sm_CodeTypeId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_PhieuNhapKho" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_PhieuNhapKho_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_PhieuNhapKho_mk_NhaCungCap_IdNhaCungCap" FOREIGN KEY ("IdNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_PhieuNhapKho_sm_CodeType_sm_CodeTypeId" FOREIGN KEY ("sm_CodeTypeId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_PhieuXuatKho" (
    "Id" uuid NOT NULL,
    "MaKho" text NULL,
    "MaDonHang" text NULL,
    "MucDichNhapKho" text NULL,
    "IdVatTu" uuid NOT NULL,
    "SoLuong" integer NULL,
    "GiaTien" numeric NULL,
    "TongTien" numeric NULL,
    "IdDuAn" uuid NOT NULL,
    "IdNhaCungCap" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_PhieuXuatKho" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_PhieuXuatKho_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_PhieuXuatKho_mk_NhaCungCap_IdNhaCungCap" FOREIGN KEY ("IdNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_VatTu_IdVatTu" ON "mk_VatTu" ("IdVatTu");

CREATE INDEX "IX_mk_PhieuNhapKho_IdDuAn" ON "mk_PhieuNhapKho" ("IdDuAn");

CREATE INDEX "IX_mk_PhieuNhapKho_IdNhaCungCap" ON "mk_PhieuNhapKho" ("IdNhaCungCap");

CREATE INDEX "IX_mk_PhieuNhapKho_sm_CodeTypeId" ON "mk_PhieuNhapKho" ("sm_CodeTypeId");

CREATE INDEX "IX_mk_PhieuXuatKho_IdDuAn" ON "mk_PhieuXuatKho" ("IdDuAn");

CREATE INDEX "IX_mk_PhieuXuatKho_IdNhaCungCap" ON "mk_PhieuXuatKho" ("IdNhaCungCap");

ALTER TABLE "mk_VatTu" ADD CONSTRAINT "FK_mk_VatTu_mk_PhieuNhapKho_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_PhieuNhapKho" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_VatTu" ADD CONSTRAINT "FK_mk_VatTu_mk_PhieuXuatKho_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_PhieuXuatKho" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240617090706_add-api-quanlyphieunhap-xuat', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" DROP CONSTRAINT "FK_mk_VatTu_mk_PhieuNhapKho_IdVatTu";

ALTER TABLE "mk_VatTu" DROP CONSTRAINT "FK_mk_VatTu_mk_PhieuXuatKho_IdVatTu";

DROP TABLE "mk_PhieuNhapKho";

DROP TABLE "mk_PhieuXuatKho";

DROP INDEX "IX_mk_VatTu_IdVatTu";

ALTER TABLE "mk_VatTu" DROP COLUMN "IdVatTu";

CREATE TABLE "mk_QuanLyPhieu" (
    "Id" uuid NOT NULL,
    "MaKho" text NULL,
    "MaDonHang" text NULL,
    "DiaDiemGiaoHang" text NULL,
    "MucDichNhapKho" text NULL,
    "LoaiPhieu" text NULL,
    "IdDuAn" uuid NOT NULL,
    "IdNhaCungCap" uuid NOT NULL,
    "sm_CodeTypeId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_QuanLyPhieu" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_QuanLyPhieu_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_QuanLyPhieu_mk_NhaCungCap_IdNhaCungCap" FOREIGN KEY ("IdNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_QuanLyPhieu_sm_CodeType_sm_CodeTypeId" FOREIGN KEY ("sm_CodeTypeId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_PhieuItem" (
    "Id" uuid NOT NULL,
    "MaVatTu" text NULL,
    "TenVatTu" text NULL,
    "DonGia" numeric NULL,
    "DonViTinh" text NULL,
    "SoLuong" integer NULL,
    "TongTien" numeric NULL,
    "IdPhieu" uuid NOT NULL,
    "IdVatTu" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_PhieuItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_PhieuItem_mk_QuanLyPhieu_IdPhieu" FOREIGN KEY ("IdPhieu") REFERENCES "mk_QuanLyPhieu" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_PhieuItem_mk_VatTu_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_PhieuItem_IdPhieu" ON "mk_PhieuItem" ("IdPhieu");

CREATE INDEX "IX_mk_PhieuItem_IdVatTu" ON "mk_PhieuItem" ("IdVatTu");

CREATE INDEX "IX_mk_QuanLyPhieu_IdDuAn" ON "mk_QuanLyPhieu" ("IdDuAn");

CREATE INDEX "IX_mk_QuanLyPhieu_IdNhaCungCap" ON "mk_QuanLyPhieu" ("IdNhaCungCap");

CREATE INDEX "IX_mk_QuanLyPhieu_sm_CodeTypeId" ON "mk_QuanLyPhieu" ("sm_CodeTypeId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240620070816_add-controller-quanlyphieu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" DROP CONSTRAINT "FK_mk_QuanLyPhieu_sm_CodeType_sm_CodeTypeId";

DROP INDEX "IX_mk_QuanLyPhieu_sm_CodeTypeId";

ALTER TABLE "mk_QuanLyPhieu" DROP COLUMN "sm_CodeTypeId";

ALTER TABLE "mk_QuanLyPhieu" ADD "TongTienThanhToan" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240621014638_add-field-DiaDiemGiaoHang', '6.0.27');

COMMIT;

START TRANSACTION;

DROP TABLE "mk_ChiPhi";

ALTER TABLE "mk_QuanLyPhieu" ADD "MaPhieu" text NULL;

CREATE TABLE "mk_ThuChi" (
    "Id" uuid NOT NULL,
    "LoaiChiPhi" text NULL,
    "MaChi" text NULL,
    "IdMaNhaCungCap" uuid NULL,
    "ChiChoMucDich" text NULL,
    "IdKhachHang" uuid NULL,
    "SoTien" numeric NULL,
    "GhiChu" text NULL,
    "IdDuAn" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_ThuChi" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_ThuChi_mk_DuAn_IdDuAn" FOREIGN KEY ("IdDuAn") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_ThuChi_mk_NhaCungCap_IdMaNhaCungCap" FOREIGN KEY ("IdMaNhaCungCap") REFERENCES "mk_NhaCungCap" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_ThuChi_sm_KhachHang_IdKhachHang" FOREIGN KEY ("IdKhachHang") REFERENCES "sm_KhachHang" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_ThuChi_IdDuAn" ON "mk_ThuChi" ("IdDuAn");

CREATE INDEX "IX_mk_ThuChi_IdKhachHang" ON "mk_ThuChi" ("IdKhachHang");

CREATE INDEX "IX_mk_ThuChi_IdMaNhaCungCap" ON "mk_ThuChi" ("IdMaNhaCungCap");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240624020916_add-api-thu-chi', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" DROP CONSTRAINT "FK_mk_BaoGiaItem_mk_SanPham_IdSanPham";

DROP INDEX "IX_mk_BaoGiaItem_IdSanPham";

ALTER TABLE "mk_BaoGiaItem" DROP COLUMN "IdSanPham";

ALTER TABLE "mk_BaoGiaItem" RENAME COLUMN "DonVi" TO "TenVatTu";

ALTER TABLE "mk_BaoGiaItem" RENAME COLUMN "BaoGiaType" TO "MaVatTu";

ALTER TABLE "mk_BaoGia" RENAME COLUMN "TongTien" TO "TongTienThanhToan";

ALTER TABLE "mk_BaoGiaItem" ADD "DonViTinh" text NULL;

ALTER TABLE "mk_BaoGia" ADD "LoaiBaoGia" text NULL;

ALTER TABLE "mk_BaoGia" ADD "MaDonHang" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240625020735_add-api-baogia', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_KiemKho" (
    "Id" uuid NOT NULL,
    "Ma" text NULL,
    "MucDich" text NULL,
    "GhiChu" text NULL,
    "ListKho" text[] NULL,
    "DenNgay" timestamp without time zone NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_KiemKho" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_KiemKho_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_XuatNhapTon" (
    "Id" uuid NOT NULL,
    "MaVatTu" text NULL,
    "TenVatTu" text NULL,
    "DonGia" numeric NULL,
    "DonViTinh" text NULL,
    "MaKho" text NULL,
    "SoLuong" integer NULL,
    "SoLuongKiemKe" integer NULL,
    "SoLuongChenhLech" integer NULL,
    "TongTien" numeric NULL,
    "LoaiXuatNhapTon" text NULL,
    "IdKiemKho" uuid NULL,
    "IdVatTu" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_XuatNhapTon" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_XuatNhapTon_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_XuatNhapTon_mk_KiemKho_IdKiemKho" FOREIGN KEY ("IdKiemKho") REFERENCES "mk_KiemKho" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_mk_XuatNhapTon_mk_VatTu_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_KiemKho_CreatedByUserId" ON "mk_KiemKho" ("CreatedByUserId");

CREATE INDEX "IX_mk_XuatNhapTon_CreatedByUserId" ON "mk_XuatNhapTon" ("CreatedByUserId");

CREATE INDEX "IX_mk_XuatNhapTon_IdKiemKho" ON "mk_XuatNhapTon" ("IdKiemKho");

CREATE INDEX "IX_mk_XuatNhapTon_IdVatTu" ON "mk_XuatNhapTon" ("IdVatTu");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240625021225_add-tbl-kiemkho-xuatnhapton', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ThuChi" ADD "NgayThuChi" timestamp without time zone NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240626071859_add-field-NgayThuChi', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" ADD "QuyCach" text NULL;

ALTER TABLE "mk_BaoGia" ADD "ReceiverDiaChi" text NULL;

ALTER TABLE "mk_BaoGia" ADD "ReceiverSoDienThoai" text NULL;

ALTER TABLE "mk_BaoGia" ADD "ReceiverTen" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240627040354_add-field-baogia', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" ADD "ReceiverMa" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240627042735_add-field-ReceiverMa', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" DROP COLUMN "ReceiverMa";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240627043912_comments-field-ReceiverMa', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" ADD "ReceiverMa" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240627044352_uncomments-field-ReceiverMa', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_KhachHang" ADD "MaSoThue" text NULL;

ALTER TABLE "mk_BaoGia" ADD "ReceiverMST" text NULL;

ALTER TABLE "mk_BaoGia" ADD "SoPhieu" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240629024912_add-field-MaSoThue-in-KhachHang', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" ADD "VAT" double precision NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240701080046_add-field-VAT', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" ADD "IdSanPham" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_mk_BaoGiaItem_IdSanPham" ON "mk_BaoGiaItem" ("IdSanPham");

ALTER TABLE "mk_BaoGiaItem" ADD CONSTRAINT "FK_mk_BaoGiaItem_mk_SanPham_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "mk_SanPham" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240702023005_add-field-idSanPham', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" ALTER COLUMN "IdVatTu" DROP NOT NULL;

ALTER TABLE "mk_BaoGiaItem" ALTER COLUMN "IdSanPham" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240702024039_add-nullfield-idSanPham-idVatTu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" ADD "ActionMadeByUserId" uuid NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "ActionMadeByUserName" text NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "ActionMadeOnDate" timestamp without time zone NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "LyDoTuChoi" text NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "TrangThai" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240702093346_add-trang-thai-quanlyphieu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" ADD "DonGia" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240702100058_add-field-DonGia-in-VatTu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" ADD "Type" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240702102533_add-field-Type-in-VatTu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" ADD "TongTien" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240704095833_add-field-TongTien', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" ADD "TongTien" numeric NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "VAT" double precision NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240705072905_add-vat-tong-tien-phieu-nhap-xuat', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" DROP CONSTRAINT "FK_mk_BaoGiaItem_mk_SanPham_IdSanPham";

ALTER TABLE "mk_Bom" DROP CONSTRAINT "FK_mk_Bom_mk_SanPham_IdSanPham";

DROP TABLE "mk_SanPham";

DROP INDEX "IX_mk_BaoGiaItem_IdSanPham";

ALTER TABLE "mk_BaoGiaItem" DROP COLUMN "IdSanPham";

ALTER TABLE "mk_Bom" ADD CONSTRAINT "FK_mk_Bom_mk_VatTu_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "mk_VatTu" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240708013605_disable-sanpham', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_NguyenVatLieu" ADD "TongTien" numeric NULL;

ALTER TABLE "mk_Bom" ADD "TongTien" numeric NULL;

ALTER TABLE "mk_Bom" ADD "TongTienThanhToan" numeric NULL;

ALTER TABLE "mk_Bom" ADD "VAT" double precision NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240709025251_add-tong-tien-boom', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "idm_User" ADD "IdChucVu" uuid NULL;

CREATE INDEX "IX_idm_User_IdChucVu" ON "idm_User" ("IdChucVu");

ALTER TABLE "idm_User" ADD CONSTRAINT "FK_idm_User_mk_ChucVu_IdChucVu" FOREIGN KEY ("IdChucVu") REFERENCES "mk_ChucVu" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240709070350_add-foreigntkey-idChucVu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" ADD "GhiChu" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240709073245_add-ghichu-baogia-item', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_KhachHang" ADD "BaoGia" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240715090723_add-field-BaoGia-in-KhachHang', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_WorkingDay" (
    "Id" uuid NOT NULL,
    "TenNgayNghi" text NOT NULL,
    "StartDate" timestamp without time zone NOT NULL,
    "EndDate" timestamp without time zone NOT NULL,
    "Month" text NULL,
    "SoNgayLamViec" integer NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_WorkingDay" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240717031502_add-api-ngaynghi', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_WorkingDay" DROP COLUMN "EndDate";

ALTER TABLE "mk_WorkingDay" DROP COLUMN "SoNgayLamViec";

ALTER TABLE "mk_WorkingDay" DROP COLUMN "StartDate";

ALTER TABLE "mk_WorkingDay" DROP COLUMN "TenNgayNghi";

ALTER TABLE "mk_WorkingDay" ALTER COLUMN "Month" TYPE integer;
UPDATE "mk_WorkingDay" SET "Month" = 0 WHERE "Month" IS NULL;
ALTER TABLE "mk_WorkingDay" ALTER COLUMN "Month" SET NOT NULL;
ALTER TABLE "mk_WorkingDay" ALTER COLUMN "Month" SET DEFAULT 0;

ALTER TABLE "mk_WorkingDay" ADD "Day" integer NOT NULL DEFAULT 0;

ALTER TABLE "mk_WorkingDay" ADD "IsOverride" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "mk_WorkingDay" ADD "Note" text NULL;

ALTER TABLE "mk_WorkingDay" ADD "OriginalType" character varying(10) NULL;

ALTER TABLE "mk_WorkingDay" ADD "Type" character varying(10) NOT NULL DEFAULT '';

ALTER TABLE "mk_WorkingDay" ADD "Year" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240717085721_fix-api-NgayLamViec', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_BHXH" (
    "Id" uuid NOT NULL,
    "FromDate" timestamp without time zone NOT NULL,
    "ToDate" timestamp without time zone NOT NULL,
    "BHXHNguoiLaoDong" numeric NULL,
    "BHYTNguoiLaoDong" numeric NULL,
    "BHTNNguoiLaoDong" numeric NULL,
    "BHXHNguoiSuDungLaoDong" numeric NULL,
    "BHYTNguoiSuDungLaoDong" numeric NULL,
    "BHTNNguoiSuDungLaoDong" numeric NULL,
    "TongNguoiLaoDong" numeric NULL,
    "TongNguoiSuDungLaoDong" numeric NULL,
    "TongTatCa" numeric NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_BHXH" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240722025111_add-api-BHXH', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_ChamCong" (
    "Id" uuid NOT NULL,
    "TenBangChamCong" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_ChamCong" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_ChamCongItem" (
    "Id" uuid NOT NULL,
    "MaSo" text NULL,
    "HoVaTen" text NULL,
    "ChucVu" text NULL,
    "NgayCong" numeric NULL,
    "LamThemNgayThuong" numeric NULL,
    "LamThemChuNhat" numeric NULL,
    "LamThemNgayLe" numeric NULL,
    "KyTen" text NULL,
    "IdChamCong" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_ChamCongItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_ChamCongItem_mk_ChamCong_IdChamCong" FOREIGN KEY ("IdChamCong") REFERENCES "mk_ChamCong" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_ChamCongItem_IdChamCong" ON "mk_ChamCongItem" ("IdChamCong");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240722083059_add-api-chamcong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCongItem" DROP COLUMN "KyTen";

ALTER TABLE "mk_WorkingDay" ADD "NgayTrongThangId" uuid NULL;

ALTER TABLE "mk_ChamCong" ADD "KichHoatBangLuong" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "mk_ChamCong" ADD "Nam" integer NOT NULL DEFAULT 0;

ALTER TABLE "mk_ChamCong" ADD "Thang" integer NOT NULL DEFAULT 0;

CREATE TABLE "NgayTrongThang" (
    "Id" uuid NOT NULL,
    "Ngay" integer NOT NULL,
    "ThuTrongTuan" text NULL,
    "LoaiNgay" text NULL,
    "Cong" numeric NULL,
    "mk_ChamCongItemId" uuid NULL,
    CONSTRAINT "PK_NgayTrongThang" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_NgayTrongThang_mk_ChamCongItem_mk_ChamCongItemId" FOREIGN KEY ("mk_ChamCongItemId") REFERENCES "mk_ChamCongItem" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_WorkingDay_NgayTrongThangId" ON "mk_WorkingDay" ("NgayTrongThangId");

CREATE INDEX "IX_NgayTrongThang_mk_ChamCongItemId" ON "NgayTrongThang" ("mk_ChamCongItemId");

ALTER TABLE "mk_WorkingDay" ADD CONSTRAINT "FK_mk_WorkingDay_NgayTrongThang_NgayTrongThangId" FOREIGN KEY ("NgayTrongThangId") REFERENCES "NgayTrongThang" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240723035910_fix-api-ChamCong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_WorkingDay" DROP CONSTRAINT "FK_mk_WorkingDay_NgayTrongThang_NgayTrongThangId";

DROP INDEX "IX_mk_WorkingDay_NgayTrongThangId";

ALTER TABLE "mk_WorkingDay" DROP COLUMN "NgayTrongThangId";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240723073319_add-field-Ma-In-User', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "idm_User" ADD "Ma" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240723080022_add-field-Ma', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCong" ADD "Date" timestamp without time zone NOT NULL DEFAULT TIMESTAMP '-infinity';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240725021823_add-field-Date', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_BangTinhLuong" (
    "Id" uuid NOT NULL,
    "TenBangTinhLuong" text NULL,
    "TenCongTy" text NULL,
    "DiaChiCongTy" text NULL,
    "ThoiGian" timestamp without time zone NOT NULL,
    "SoNgayCongTrongThang" integer NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_BangTinhLuong" PRIMARY KEY ("Id")
);

CREATE TABLE "mk_BangLuongItem" (
    "Id" uuid NOT NULL,
    "MaSo" text NULL,
    "TenNhanVien" text NULL,
    "ChucVu" text NULL,
    "LuongCoBan" numeric NULL,
    "Tong" numeric NULL,
    "NgayCong" numeric NULL,
    "Luong" numeric NULL,
    "ThucLinh" numeric NULL,
    "IdBangTinhLuong" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_BangLuongItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_BangLuongItem_mk_BangTinhLuong_IdBangTinhLuong" FOREIGN KEY ("IdBangTinhLuong") REFERENCES "mk_BangTinhLuong" ("Id") ON DELETE CASCADE
);

CREATE TABLE "mk_CacKhoanTroCap" (
    "Id" uuid NOT NULL,
    "AnCa" numeric NULL,
    "DienThoai" numeric NULL,
    "TrangPhuc" numeric NULL,
    "mk_BangLuongItemId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_CacKhoanTroCap" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_CacKhoanTroCap_mk_BangLuongItem_mk_BangLuongItemId" FOREIGN KEY ("mk_BangLuongItemId") REFERENCES "mk_BangLuongItem" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_BangLuongItem_IdBangTinhLuong" ON "mk_BangLuongItem" ("IdBangTinhLuong");

CREATE INDEX "IX_mk_CacKhoanTroCap_mk_BangLuongItemId" ON "mk_CacKhoanTroCap" ("mk_BangLuongItemId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240725095548_add-api-bang-tinh-luong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BangTinhLuong" ADD "MaSoThue" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240725101440_add-field-MaSoThue', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_CacKhoanTroCap" DROP CONSTRAINT "FK_mk_CacKhoanTroCap_mk_BangLuongItem_mk_BangLuongItemId";

DROP INDEX "IX_mk_CacKhoanTroCap_mk_BangLuongItemId";

ALTER TABLE "mk_CacKhoanTroCap" DROP COLUMN "mk_BangLuongItemId";

ALTER TABLE "mk_BangLuongItem" ADD "CacKhoanTroCapId" uuid NULL;

CREATE INDEX "IX_mk_BangLuongItem_CacKhoanTroCapId" ON "mk_BangLuongItem" ("CacKhoanTroCapId");

ALTER TABLE "mk_BangLuongItem" ADD CONSTRAINT "FK_mk_BangLuongItem_mk_CacKhoanTroCap_CacKhoanTroCapId" FOREIGN KEY ("CacKhoanTroCapId") REFERENCES "mk_CacKhoanTroCap" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240726072558_count-field-Tong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCong" RENAME COLUMN "KichHoatBangLuong" TO "KichHoatBangChamCong";

ALTER TABLE "mk_BangTinhLuong" ADD "KichHoatBangLuong" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240729095739_add-field-KichHoatBangLuong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "NgayTrongThang" ALTER COLUMN "Cong" TYPE text;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240730075850_change-type-Cong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BangTinhLuong" ADD "IdBangChamCongActive" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240802020227_add-field-IdBangChamCongActive', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCong" ADD "Cong" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240805040337_add-field-Cong', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCongItem" ADD "Date" timestamp without time zone NOT NULL DEFAULT TIMESTAMP '-infinity';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240806071407_add-field-Date-in-ChamCongItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCongItem" ALTER COLUMN "Date" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240806071810_set-null-field-Date', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "mk_CauHinhNhanSu" (
    "Id" uuid NOT NULL,
    "Ma" text NULL,
    "TenNhanSu" text NULL,
    "IdChucVu" uuid NULL,
    "LuongCoBan" numeric NULL,
    "AnCa" numeric NULL,
    "DienThoai" numeric NULL,
    "TrangPhuc" numeric NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_mk_CauHinhNhanSu" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_mk_CauHinhNhanSu_mk_ChucVu_IdChucVu" FOREIGN KEY ("IdChucVu") REFERENCES "mk_ChucVu" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_CauHinhNhanSu_IdChucVu" ON "mk_CauHinhNhanSu" ("IdChucVu");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240807034219_add-api-CauHinhNhanSu', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "fm_Search_Sample" (
    "Id" uuid NOT NULL,
    "Title" text NULL,
    "QueryJsonString" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_fm_Search_Sample" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240808095047_add-demo-search-template', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "idm_User" ADD "IdPhongBan" uuid NULL;

CREATE INDEX "IX_idm_User_IdPhongBan" ON "idm_User" ("IdPhongBan");

ALTER TABLE "idm_User" ADD CONSTRAINT "FK_idm_User_mk_PhongBan_IdPhongBan" FOREIGN KEY ("IdPhongBan") REFERENCES "mk_PhongBan" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240809032047_add-field-idPhongBan', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_CauHinhNhanSu" DROP CONSTRAINT "FK_mk_CauHinhNhanSu_mk_ChucVu_IdChucVu";

ALTER TABLE "mk_CauHinhNhanSu" DROP COLUMN "Ma";

ALTER TABLE "mk_CauHinhNhanSu" DROP COLUMN "TenNhanSu";

ALTER TABLE "mk_CauHinhNhanSu" RENAME COLUMN "IdChucVu" TO "IdUser";

ALTER INDEX "IX_mk_CauHinhNhanSu_IdChucVu" RENAME TO "IX_mk_CauHinhNhanSu_IdUser";

ALTER TABLE "mk_CauHinhNhanSu" ADD CONSTRAINT "FK_mk_CauHinhNhanSu_idm_User_IdUser" FOREIGN KEY ("IdUser") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240809044252_mapper-fields-inCauHinhNhanSu', '6.0.27');

COMMIT;

START TRANSACTION;

UPDATE "idm_User" SET "CreatedOnDate" = TIMESTAMP '-infinity' WHERE "CreatedOnDate" IS NULL;
ALTER TABLE "idm_User" ALTER COLUMN "CreatedOnDate" SET NOT NULL;
ALTER TABLE "idm_User" ALTER COLUMN "CreatedOnDate" SET DEFAULT TIMESTAMP '-infinity';

ALTER TABLE "idm_User" ADD "CreatedByUserId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "idm_User" ADD "CreatedByUserName" text NULL;

ALTER TABLE "idm_User" ADD "LastModifiedByUserId" uuid NULL;

ALTER TABLE "idm_User" ADD "LastModifiedByUserName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240812092735_fix-BaseTableService', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BangLuongItem" ADD "Order" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240812101811_add-field-Order', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCongItem" ADD "Order" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240813023121_fix-sort-OrderBy', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_CauHinhNhanSu" DROP CONSTRAINT "PK_mk_CauHinhNhanSu";

DROP INDEX "IX_mk_CauHinhNhanSu_IdUser";

ALTER TABLE "mk_CauHinhNhanSu" DROP COLUMN "Id";

UPDATE "mk_CauHinhNhanSu" SET "IdUser" = '00000000-0000-0000-0000-000000000000' WHERE "IdUser" IS NULL;
ALTER TABLE "mk_CauHinhNhanSu" ALTER COLUMN "IdUser" SET NOT NULL;
ALTER TABLE "mk_CauHinhNhanSu" ALTER COLUMN "IdUser" SET DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "mk_CauHinhNhanSu" ADD CONSTRAINT "PK_mk_CauHinhNhanSu" PRIMARY KEY ("IdUser");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240813033129_fix-Id', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BHXH" DROP COLUMN "FromDate";

ALTER TABLE "mk_BHXH" DROP COLUMN "ToDate";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240813081856_remove-field-FromDate-ToDate', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ChamCongItem" ADD "AnCa" numeric NULL;

ALTER TABLE "mk_ChamCongItem" ADD "DienThoai" numeric NULL;

ALTER TABLE "mk_ChamCongItem" ADD "LuongCoBan" numeric NULL;

ALTER TABLE "mk_ChamCongItem" ADD "TrangPhuc" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhtnNLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhtnNSDLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhxhNLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhxhNSDLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhytNLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "BhytNSDLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "GhiChu" text NULL;

ALTER TABLE "mk_BangLuongItem" ADD "GiamTruBanThan" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "NgayNhan" timestamp without time zone NULL;

ALTER TABLE "mk_BangLuongItem" ADD "SoNguoiPhuThuoc" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "SoTienPhuThuoc" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "ThuNhapCaNhan" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "ThuNhapTinhThue" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "ThueTNCNPhaiNop" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "TongNLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "TongNSDLD" numeric NULL;

ALTER TABLE "mk_BangLuongItem" ADD "TongTatCa" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240815034055_add-fields-BangTinhLuongItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_NhaCungCap" ADD "Description" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "Email" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "GiaMacDinh" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "HinhThucThanhToan" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "MaSoThue" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "NguoiPhuTrach" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "TrangThai" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240816042203_add-fields-to-nha-cung-cap', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_NhaCungCap" ADD "Fax" text NULL;

ALTER TABLE "mk_NhaCungCap" ADD "Website" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240820021126_add-column-website-fax', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_VatTu" ADD "TrangThai" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240821030948_add-field-TrangThai', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGiaItem" DROP CONSTRAINT "FK_mk_BaoGiaItem_mk_VatTu_IdVatTu";

ALTER TABLE "mk_Bom" DROP CONSTRAINT "FK_mk_Bom_mk_VatTu_IdSanPham";

ALTER TABLE "mk_NguyenVatLieu" DROP CONSTRAINT "FK_mk_NguyenVatLieu_mk_VatTu_IdVatTu";

ALTER TABLE "mk_PhieuItem" DROP CONSTRAINT "FK_mk_PhieuItem_mk_VatTu_IdVatTu";

ALTER TABLE "mk_XuatNhapTon" DROP CONSTRAINT "FK_mk_XuatNhapTon_mk_VatTu_IdVatTu";

DROP TABLE "mk_VatTu";

CREATE TABLE "sm_Product" (
    "Id" uuid NOT NULL,
    "Type" text NULL,
    "Code" text NULL,
    "Name" text NULL,
    "Unit" text NULL,
    "IsActive" text NULL,
    "PurchaseUnitPrice" numeric NOT NULL,
    "SellingUnitPrice" numeric NOT NULL,
    "Description" text NULL,
    "Attachments" jsonb NULL,
    "Note" text NULL,
    "ProductGroupId" uuid NOT NULL,
    "IdNhomVatTu" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Product" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Product_mk_NhomVatTu_IdNhomVatTu" FOREIGN KEY ("IdNhomVatTu") REFERENCES "mk_NhomVatTu" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Product_IdNhomVatTu" ON "sm_Product" ("IdNhomVatTu");

ALTER TABLE "mk_BaoGiaItem" ADD CONSTRAINT "FK_mk_BaoGiaItem_sm_Product_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_Bom" ADD CONSTRAINT "FK_mk_Bom_sm_Product_IdSanPham" FOREIGN KEY ("IdSanPham") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_NguyenVatLieu" ADD CONSTRAINT "FK_mk_NguyenVatLieu_sm_Product_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_PhieuItem" ADD CONSTRAINT "FK_mk_PhieuItem_sm_Product_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_XuatNhapTon" ADD CONSTRAINT "FK_mk_XuatNhapTon_sm_Product_IdVatTu" FOREIGN KEY ("IdVatTu") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822020313_create-table-Product', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Product" DROP COLUMN "IsActive";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822021343_remove-isActive', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "IsActive" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822021506_change-type-isActive', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" DROP CONSTRAINT "FK_mk_BaoGia_sm_KhachHang_IdKhachHang";

ALTER TABLE "mk_ThuChi" DROP CONSTRAINT "FK_mk_ThuChi_sm_KhachHang_IdKhachHang";

ALTER TABLE "sm_LichSuChamSoc" DROP CONSTRAINT "FK_sm_LichSuChamSoc_sm_Customer_sm_CustomerId";

DROP TABLE "sm_KhachHang";

DROP INDEX "IX_sm_LichSuChamSoc_sm_CustomerId";

DROP INDEX "IX_mk_ThuChi_IdKhachHang";

ALTER TABLE "sm_LichSuChamSoc" DROP COLUMN "KhachHangId";

ALTER TABLE "sm_LichSuChamSoc" ADD "sm_CustomerId" uuid NULL;

ALTER TABLE "mk_ThuChi" ADD "sm_CustomerId" uuid NULL;

CREATE TABLE "sm_Customer" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Name" text NULL,
    "TaxCode" text NULL,
    "Note" text NULL,
    "PhoneNumber" text NULL,
    "Address" text NULL,
    "BaoGia" text NULL,
    "Fax" text NULL,
    "Sex" text NULL,
    "Email" text NULL,
    "Website" text NULL,
    "Birthdate" timestamp without time zone NULL,
    "LinkFacebook" text NULL,
    "LinkTiktok" text NULL,
    "LinkTelegram" text NULL,
    "Type" text[] NULL,
    "InitialRequirement" text NULL,
    "InformationToCopy" text NULL,
    "TotalQuotationCount" integer NOT NULL,
    "LastCareOnDate" timestamp without time zone NULL,
    "TotalCareTimes" integer NOT NULL,
    "NhuCauBanDau" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Customer" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Customer_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_LichSuChamSoc_sm_CustomerId" ON "sm_LichSuChamSoc" ("sm_CustomerId");

CREATE INDEX "IX_mk_ThuChi_sm_CustomerId" ON "mk_ThuChi" ("sm_CustomerId");

CREATE INDEX "IX_sm_Customer_CreatedByUserId" ON "sm_Customer" ("CreatedByUserId");

ALTER TABLE "mk_BaoGia" ADD CONSTRAINT "FK_mk_BaoGia_sm_Customer_IdKhachHang" FOREIGN KEY ("IdKhachHang") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_ThuChi" ADD CONSTRAINT "FK_mk_ThuChi_sm_Customer_sm_CustomerId" FOREIGN KEY ("sm_CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_LichSuChamSoc" ADD CONSTRAINT "FK_sm_LichSuChamSoc_sm_Customer_sm_CustomerId" FOREIGN KEY ("sm_CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822031836_add-table-customer', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ThuChi" DROP CONSTRAINT "FK_mk_ThuChi_sm_Customer_sm_CustomerId";

ALTER TABLE "sm_LichSuChamSoc" DROP CONSTRAINT "FK_sm_LichSuChamSoc_sm_Customer_sm_CustomerId";

DROP INDEX "IX_sm_LichSuChamSoc_sm_CustomerId";

ALTER TABLE "sm_LichSuChamSoc" DROP COLUMN "sm_CustomerId";

ALTER TABLE "mk_ThuChi" DROP COLUMN "IdKhachHang";

ALTER TABLE "mk_ThuChi" RENAME COLUMN "sm_CustomerId" TO "CustomerId";

ALTER INDEX "IX_mk_ThuChi_sm_CustomerId" RENAME TO "IX_mk_ThuChi_CustomerId";

ALTER TABLE "sm_LichSuChamSoc" ADD "CustomerId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_LichSuChamSoc_CustomerId" ON "sm_LichSuChamSoc" ("CustomerId");

ALTER TABLE "mk_ThuChi" ADD CONSTRAINT "FK_mk_ThuChi_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_LichSuChamSoc" ADD CONSTRAINT "FK_sm_LichSuChamSoc_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822033043_update-table-customer', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "TrangThai" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822045956_add-field-isActive', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Customer" RENAME COLUMN "TrangThai" TO "IsActive";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822064128_rename-field-isActive', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "DistrictCode" text NULL;

ALTER TABLE "sm_Customer" ADD "DistrictName" text NULL;

ALTER TABLE "sm_Customer" ADD "ProvinceCode" text NULL;

ALTER TABLE "sm_Customer" ADD "ProvinceName" text NULL;

ALTER TABLE "sm_Customer" ADD "WardCode" text NULL;

ALTER TABLE "sm_Customer" ADD "WardName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822065711_add-field-address', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Customer" DROP COLUMN "DistrictCode";

ALTER TABLE "sm_Customer" DROP COLUMN "ProvinceCode";

ALTER TABLE "sm_Customer" DROP COLUMN "WardCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822100223_change-type-address', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "DistrictCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Customer" ADD "ProvinceCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Customer" ADD "WardCode" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240822100319_add-address', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" DROP CONSTRAINT "FK_mk_QuanLyPhieu_mk_NhaCungCap_IdNhaCungCap";

ALTER TABLE "mk_ThuChi" DROP CONSTRAINT "FK_mk_ThuChi_mk_NhaCungCap_IdMaNhaCungCap";

DROP TABLE "mk_NhaCungCap";

DROP INDEX "IX_mk_ThuChi_IdMaNhaCungCap";

DROP INDEX "IX_mk_QuanLyPhieu_IdNhaCungCap";

ALTER TABLE "mk_ThuChi" ADD "sm_SupplierId" uuid NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "sm_SupplierId" uuid NULL;

CREATE TABLE "sm_Supplier" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Name" text NULL,
    "TaxCode" text NULL,
    "Email" text NULL,
    "Fax" text NULL,
    "Website" text NULL,
    "NguoiPhuTrach" text NULL,
    "IsActive" boolean NOT NULL,
    "Address" text NULL,
    "PhoneNumber" text NULL,
    "Note" text NULL,
    "Description" text NULL,
    "TotalDebtAmount" numeric NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Supplier" PRIMARY KEY ("Id")
);

CREATE INDEX "IX_mk_ThuChi_sm_SupplierId" ON "mk_ThuChi" ("sm_SupplierId");

CREATE INDEX "IX_mk_QuanLyPhieu_sm_SupplierId" ON "mk_QuanLyPhieu" ("sm_SupplierId");

ALTER TABLE "mk_QuanLyPhieu" ADD CONSTRAINT "FK_mk_QuanLyPhieu_sm_Supplier_sm_SupplierId" FOREIGN KEY ("sm_SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_ThuChi" ADD CONSTRAINT "FK_mk_ThuChi_sm_Supplier_sm_SupplierId" FOREIGN KEY ("sm_SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240824014252_rename-sm-nhaCungCap', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_ThuChi" DROP CONSTRAINT "FK_mk_ThuChi_sm_Supplier_sm_SupplierId";

DROP INDEX "IX_mk_ThuChi_sm_SupplierId";

ALTER TABLE "mk_ThuChi" RENAME COLUMN "sm_SupplierId" TO "IdSupplier";

CREATE INDEX "IX_mk_ThuChi_IdMaNhaCungCap" ON "mk_ThuChi" ("IdMaNhaCungCap");

ALTER TABLE "mk_ThuChi" ADD CONSTRAINT "FK_mk_ThuChi_sm_Supplier_IdMaNhaCungCap" FOREIGN KEY ("IdMaNhaCungCap") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240824015929_update-table-thu-chi', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" DROP CONSTRAINT "FK_mk_QuanLyPhieu_sm_Supplier_sm_SupplierId";

DROP INDEX "IX_mk_QuanLyPhieu_sm_SupplierId";

ALTER TABLE "mk_QuanLyPhieu" DROP COLUMN "sm_SupplierId";

CREATE INDEX "IX_mk_QuanLyPhieu_IdNhaCungCap" ON "mk_QuanLyPhieu" ("IdNhaCungCap");

ALTER TABLE "mk_QuanLyPhieu" ADD CONSTRAINT "FK_mk_QuanLyPhieu_sm_Supplier_IdNhaCungCap" FOREIGN KEY ("IdNhaCungCap") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240824020203_update-table-quan-ly-phieu', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Supplier" ADD "DistrictCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Supplier" ADD "DistrictName" text NULL;

ALTER TABLE "sm_Supplier" ADD "ProvinceCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Supplier" ADD "ProvinceName" text NULL;

ALTER TABLE "sm_Supplier" ADD "WardCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Supplier" ADD "WardName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240824024754_add-fields-sm_Supplier', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_BaoGia" RENAME COLUMN "ReceiverTen" TO "CustomerTaxCode";

ALTER TABLE "mk_BaoGia" RENAME COLUMN "ReceiverSoDienThoai" TO "CustomerPhoneNumber";

ALTER TABLE "mk_BaoGia" RENAME COLUMN "ReceiverMa" TO "CustomerName";

ALTER TABLE "mk_BaoGia" RENAME COLUMN "ReceiverMST" TO "CustomerCode";

ALTER TABLE "mk_BaoGia" RENAME COLUMN "ReceiverDiaChi" TO "CustomerAddress";

CREATE TABLE "sm_Cashbook_Transaction" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "PayerGroup" text NULL,
    "SupplierId" uuid NULL,
    "CustomerId" uuid NULL,
    "ProjectId" uuid NULL,
    "Purpose" text NULL,
    "Amount" numeric NULL,
    "PaymentMethod" text NULL,
    "ReceiptDate" timestamp without time zone NOT NULL,
    "Description" text NULL,
    "isStatus" boolean NOT NULL,
    "TransactionType" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Cashbook_Transaction" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Cashbook_Transaction_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Supplier_SupplierId" FOREIGN KEY ("SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Cashbook_Transaction_CustomerId" ON "sm_Cashbook_Transaction" ("CustomerId");

CREATE INDEX "IX_sm_Cashbook_Transaction_ProjectId" ON "sm_Cashbook_Transaction" ("ProjectId");

CREATE INDEX "IX_sm_Cashbook_Transaction_SupplierId" ON "sm_Cashbook_Transaction" ("SupplierId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240826100823_add-api-cashbook_transaction', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "sm_Stock_Transaction" (
    "Id" uuid NOT NULL,
    "TypeCode" text NULL,
    "TypeName" text NULL,
    "ProductCode" text NULL,
    "ProductName" text NULL,
    "Description" text NULL,
    "Note" text NULL,
    "Amount" numeric NULL,
    "Unit" text NULL,
    "WareCode" text NULL,
    "Quantity" integer NULL,
    "OpeningInventory" integer NULL,
    "ClosingInventory" integer NULL,
    "InventoryQuantity" integer NULL,
    "DifferentAmount" integer NULL,
    "SubTotal" numeric NULL,
    "CheckInventoryId" uuid NULL,
    "ProductId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Stock_Transaction" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Stock_Transaction_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Stock_Transaction_mk_KiemKho_CheckInventoryId" FOREIGN KEY ("CheckInventoryId") REFERENCES "mk_KiemKho" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Stock_Transaction_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Stock_Transaction_CheckInventoryId" ON "sm_Stock_Transaction" ("CheckInventoryId");

CREATE INDEX "IX_sm_Stock_Transaction_CreatedByUserId" ON "sm_Stock_Transaction" ("CreatedByUserId");

CREATE INDEX "IX_sm_Stock_Transaction_ProductId" ON "sm_Stock_Transaction" ("ProductId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240828040603_createTableStockTransaction', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" DROP CONSTRAINT "FK_mk_QuanLyPhieu_mk_DuAn_IdDuAn";

ALTER TABLE "mk_QuanLyPhieu" DROP CONSTRAINT "FK_mk_QuanLyPhieu_sm_Supplier_IdNhaCungCap";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "TrangThai" TO "WareCode";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "TongTienThanhToan" TO "TotalAmount";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "TongTien" TO "SubTotal";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "MucDichNhapKho" TO "TypeName";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "MaPhieu" TO "TypeCode";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "MaKho" TO "Status";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "MaDonHang" TO "Reason";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "LoaiPhieu" TO "OrderCode";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "IdNhaCungCap" TO "SupplierId";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "IdDuAn" TO "ProjectId";

ALTER TABLE "mk_QuanLyPhieu" RENAME COLUMN "DiaDiemGiaoHang" TO "Address";

ALTER INDEX "IX_mk_QuanLyPhieu_IdNhaCungCap" RENAME TO "IX_mk_QuanLyPhieu_SupplierId";

ALTER INDEX "IX_mk_QuanLyPhieu_IdDuAn" RENAME TO "IX_mk_QuanLyPhieu_ProjectId";

ALTER TABLE "mk_QuanLyPhieu" ADD CONSTRAINT "FK_mk_QuanLyPhieu_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

ALTER TABLE "mk_QuanLyPhieu" ADD CONSTRAINT "FK_mk_QuanLyPhieu_sm_Supplier_SupplierId" FOREIGN KEY ("SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240828074302_updateFieldDocument', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_QuanLyPhieu" ADD "Note" text NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "PaymentStatus" text NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "ReceiveInventoryStatus" text NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "TotalDiscountsAccount" numeric NULL;

ALTER TABLE "mk_QuanLyPhieu" ADD "TotalTaxAccount" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240830021856_addFieldToTable', '6.0.27');

COMMIT;

START TRANSACTION;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240830022821_createTablePhieuItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_PhieuItem" ADD "DiscountPercent" integer NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240830041153_addFieldDiscountPercent', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "isStatus" TO "IsActive";

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "PaymentMethod" TO "TypeCode";

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "PayerGroup" TO "SubTypeCode";

ALTER TABLE "sm_Cashbook_Transaction" ALTER COLUMN "ReceiptDate" DROP NOT NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "ClosingBanlance" numeric NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "Note" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "OpeningBalance" numeric NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "OriginalDocumentCode" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "PaymentGroupCode" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "PaymentMethodCode" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "Reference" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240830072912_update-table-cashbook', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" DROP CONSTRAINT "FK_sm_Cashbook_Transaction_mk_DuAn_ProjectId";

ALTER TABLE "sm_Cashbook_Transaction" DROP CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Customer_CustomerId";

ALTER TABLE "sm_Cashbook_Transaction" DROP CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Supplier_SupplierId";

DROP INDEX "IX_sm_Cashbook_Transaction_CustomerId";

DROP INDEX "IX_sm_Cashbook_Transaction_ProjectId";

DROP INDEX "IX_sm_Cashbook_Transaction_SupplierId";

ALTER TABLE "sm_Cashbook_Transaction" DROP COLUMN "CustomerId";

ALTER TABLE "sm_Cashbook_Transaction" DROP COLUMN "ProjectId";

ALTER TABLE "sm_Cashbook_Transaction" DROP COLUMN "SupplierId";

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "PaymentGroupCode" TO "EntityUrl";

ALTER TABLE "sm_Cashbook_Transaction" ADD "EntityCode" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "EntityId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "sm_Cashbook_Transaction" ADD "EntityName" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "EntityTypeCode" text NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "EntityTypeName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240904101439_change-field-CashbookTransation', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "sm_PurchaseOrder" (
    "Id" uuid NOT NULL,
    "TypeCode" text NULL,
    "TypeName" text NULL,
    "Reference" text NULL,
    "OrderCode" text NULL,
    "Quantity" integer NOT NULL,
    "TotalDiscountsAccount" numeric NULL,
    "TotalRecieptInventory" numeric NULL,
    "DiscountCode" text NULL,
    "SubTotal" numeric NULL,
    "Status" text NULL,
    "PaymentStatus" text NULL,
    "PaymentMethod" text NULL,
    "Tags" text NULL,
    "Note" text NULL,
    "PackageAndDeliveryType" text NULL,
    "InvoiceRecieptAddress" text NULL,
    "DeliveryAddress" text NULL,
    "CustomerId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_PurchaseOrder" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_PurchaseOrder_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_PurchaseOrderItem" (
    "Id" uuid NOT NULL,
    "ProductCode" text NULL,
    "ProductName" text NULL,
    "Quantity" integer NOT NULL,
    "Amount" numeric NULL,
    "Unit" text NULL,
    "TotalAmount" numeric NOT NULL,
    "PurchaseOrderId" uuid NOT NULL,
    "ProductId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_PurchaseOrderItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_PurchaseOrderItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_PurchaseOrderItem_sm_PurchaseOrder_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "sm_PurchaseOrder" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_PurchaseOrder_CustomerId" ON "sm_PurchaseOrder" ("CustomerId");

CREATE INDEX "IX_sm_PurchaseOrderItem_ProductId" ON "sm_PurchaseOrderItem" ("ProductId");

CREATE INDEX "IX_sm_PurchaseOrderItem_PurchaseOrderId" ON "sm_PurchaseOrderItem" ("PurchaseOrderId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240905063355_addapipurchase_order', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "TotalAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240906144020_updateFieldPurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ADD "DiscountPercent" integer NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240906152239_updateFieldPurchaseOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "SubTotal" TO "TotalRecieptInventory";

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "InventoryQuantity" TO "RecieptInventory";

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "DifferentAmount" TO "InventoryIncreased";

ALTER TABLE "mk_KiemKho" RENAME COLUMN "MucDich" TO "WareCode";

ALTER TABLE "mk_KiemKho" RENAME COLUMN "Ma" TO "TypeName";

ALTER TABLE "mk_KiemKho" RENAME COLUMN "ListKho" TO "ListWare";

ALTER TABLE "mk_KiemKho" RENAME COLUMN "GhiChu" TO "TypeCode";

ALTER TABLE "mk_KiemKho" RENAME COLUMN "DenNgay" TO "ToDate";

ALTER TABLE "sm_Stock_Transaction" ADD "ExportInventory" integer NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "InventoryDecreased" integer NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "TotalExportInventory" numeric NULL;

ALTER TABLE "mk_KiemKho" ADD "CheckInventoryCode" text NULL;

ALTER TABLE "mk_KiemKho" ADD "Note" text NULL;

ALTER TABLE "mk_KiemKho" ADD "Tags" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909070505_testmigration', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "sm_Quotation" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "CustomerId" uuid NULL,
    "CustomerCode" text NULL,
    "CustomerName" text NULL,
    "CustomerTaxCode" text NULL,
    "CustomerAddress" text NULL,
    "CustomerPhoneNumber" text NULL,
    "DeliveryAddress" text NULL,
    "ProjectId" uuid NULL,
    "ProjectCode" text NULL,
    "ProjectName" text NULL,
    "OrderCode" text NULL,
    "TypeCode" text NULL,
    "DueDate" timestamp without time zone NOT NULL,
    "Note" text NULL,
    "VatPercent" numeric NOT NULL,
    "SubTotalAmount" numeric NOT NULL,
    "VatAmount" numeric NOT NULL,
    "TotalAmount" numeric NOT NULL,
    "DiscountAmount" numeric NOT NULL,
    "ShippingCostAmount" numeric NOT NULL,
    "OtherCostAmount" numeric NOT NULL,
    "PaymentMethodCode" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Quotation" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_QuotationItem" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Name" text NULL,
    "Unit" text NULL,
    "Quantity" numeric NOT NULL,
    "UnitPrice" numeric NOT NULL,
    "DiscountAmount" numeric NOT NULL,
    "LineAmount" numeric NOT NULL,
    "LineNumber" integer NOT NULL,
    "sm_QuotationId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_QuotationItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_QuotationItem_sm_Quotation_sm_QuotationId" FOREIGN KEY ("sm_QuotationId") REFERENCES "sm_Quotation" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_QuotationItem_sm_QuotationId" ON "sm_QuotationItem" ("sm_QuotationId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909070806_add-api-Quotation', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_KiemKho" ADD "Status" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909071511_addFieldStatus', '6.0.27');

COMMIT;

START TRANSACTION;

DROP TABLE "mk_BaoGiaItem";

DROP TABLE "mk_BaoGia";

ALTER TABLE "sm_Quotation" ADD "mk_DuAnId" uuid NULL;

CREATE INDEX "IX_sm_Quotation_mk_DuAnId" ON "sm_Quotation" ("mk_DuAnId");

ALTER TABLE "sm_Quotation" ADD CONSTRAINT "FK_sm_Quotation_mk_DuAn_mk_DuAnId" FOREIGN KEY ("mk_DuAnId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909093901_remove-api-BaoGia', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Product" DROP CONSTRAINT "FK_sm_Product_mk_NhomVatTu_IdNhomVatTu";

DROP INDEX "IX_sm_Product_IdNhomVatTu";

ALTER TABLE "sm_Product" DROP COLUMN "IdNhomVatTu";

CREATE INDEX "IX_sm_Product_ProductGroupId" ON "sm_Product" ("ProductGroupId");

ALTER TABLE "sm_Product" ADD CONSTRAINT "FK_sm_Product_mk_NhomVatTu_ProductGroupId" FOREIGN KEY ("ProductGroupId") REFERENCES "mk_NhomVatTu" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909095357_change-fk-smProduct', '6.0.27');

COMMIT;

START TRANSACTION;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909095449_rename-FK-smProduct', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "Purpose" TO "PurposeCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240909124742_add-field-purpose-code', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" ADD "LineNote" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240911101636_add-field-LineNote', '6.0.27');

COMMIT;

START TRANSACTION;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240911102731_add-field-DiscountReason-2', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "mk_PhieuItem" ADD "sm_SaleOrdersId" uuid NULL;

CREATE TABLE "sm_SaleOrders" (
    "Id" uuid NOT NULL,
    "WareCode" text NULL,
    "TypeCode" text NULL,
    "OrderCode" text NULL,
    "Address" text NULL,
    "Reason" text NULL,
    "TypeName" text NULL,
    "SubTotal" numeric NULL,
    "TotalAmount" numeric NULL,
    "VAT" double precision NOT NULL,
    "TotalDiscountsAccount" numeric NULL,
    "TotalTaxAccount" numeric NULL,
    "ReceiveInventoryStatus" text NULL,
    "PaymentStatus" text NULL,
    "Note" text NULL,
    "mk_DuAnId" uuid NULL,
    "SupplierId" uuid NOT NULL,
    "Status" text NULL,
    "LyDoTuChoi" text NULL,
    "ActionMadeByUserId" uuid NULL,
    "ActionMadeByUserName" text NULL,
    "ActionMadeOnDate" timestamp without time zone NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_SaleOrders" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_SaleOrders_mk_DuAn_mk_DuAnId" FOREIGN KEY ("mk_DuAnId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SaleOrders_sm_Supplier_SupplierId" FOREIGN KEY ("SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_mk_PhieuItem_sm_SaleOrdersId" ON "mk_PhieuItem" ("sm_SaleOrdersId");

CREATE INDEX "IX_sm_SaleOrders_mk_DuAnId" ON "sm_SaleOrders" ("mk_DuAnId");

CREATE INDEX "IX_sm_SaleOrders_SupplierId" ON "sm_SaleOrders" ("SupplierId");

ALTER TABLE "mk_PhieuItem" ADD CONSTRAINT "FK_mk_PhieuItem_sm_SaleOrders_sm_SaleOrdersId" FOREIGN KEY ("sm_SaleOrdersId") REFERENCES "sm_SaleOrders" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240911132439_createTableSaleOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" DROP CONSTRAINT "FK_sm_SaleOrders_mk_DuAn_mk_DuAnId";

DROP INDEX "IX_sm_SaleOrders_mk_DuAnId";

ALTER TABLE "sm_SaleOrders" DROP COLUMN "mk_DuAnId";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240911132820_updateTable', '6.0.27');

COMMIT;

START TRANSACTION;

DROP TABLE "mk_PhieuItem";

DROP TABLE "mk_QuanLyPhieu";

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "TotalDiscountsAccount" TO "TotalHalfPayment";

ALTER TABLE "sm_SaleOrders" ADD "HalfPayment" jsonb NULL;

ALTER TABLE "sm_SaleOrders" ADD "OtherCost" jsonb NULL;

ALTER TABLE "sm_SaleOrders" ADD "TotalDiscountsAccounts" numeric NULL;

ALTER TABLE "sm_SaleOrders" ADD "mk_DuAnId" uuid NULL;

CREATE TABLE "sm_SaleOrderItem" (
    "Id" uuid NOT NULL,
    "ProductCode" text NULL,
    "ProductName" text NULL,
    "Amout" numeric NULL,
    "Unit" text NULL,
    "Quantity" integer NULL,
    "TotalAmount" numeric NULL,
    "DiscountPercent" integer NULL,
    "IdPhieu" uuid NOT NULL,
    "ProductId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_SaleOrderItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_SaleOrderItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SaleOrderItem_sm_SaleOrders_IdPhieu" FOREIGN KEY ("IdPhieu") REFERENCES "sm_SaleOrders" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_SaleOrders_mk_DuAnId" ON "sm_SaleOrders" ("mk_DuAnId");

CREATE INDEX "IX_sm_SaleOrderItem_IdPhieu" ON "sm_SaleOrderItem" ("IdPhieu");

CREATE INDEX "IX_sm_SaleOrderItem_ProductId" ON "sm_SaleOrderItem" ("ProductId");

ALTER TABLE "sm_SaleOrders" ADD CONSTRAINT "FK_sm_SaleOrders_mk_DuAn_mk_DuAnId" FOREIGN KEY ("mk_DuAnId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240911160501_addTableItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" ADD "TotalPaymentContinue" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240912041841_updateFieldToTableSaleOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Quotation" ADD "PaymentMethodName" text NULL;

ALTER TABLE "sm_Quotation" ADD "Status" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240912150819_add-field-Status', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "Action" text NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "OriginalDocumentCode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240913024647_add-field-orginalDocument', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "ProjectId" uuid NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240913050650_add-projectId-tbl-cashbook_transaction', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE INDEX "IX_sm_Cashbook_Transaction_ProjectId" ON "sm_Cashbook_Transaction" ("ProjectId");

ALTER TABLE "sm_Cashbook_Transaction" ADD CONSTRAINT "FK_sm_Cashbook_Transaction_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240913080238_add-projectId', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" DROP COLUMN "PackageAndDeliveryType";

ALTER TABLE "sm_PurchaseOrder" ADD "HalfPayment" jsonb NULL;

ALTER TABLE "sm_PurchaseOrder" ADD "TotalHalfPayment" numeric NULL;

ALTER TABLE "sm_PurchaseOrder" ADD "TotalPaymentContinue" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240913105456_updateTablePurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "WareCode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240913135506_addFieldWareCode', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "OriginalDocumentType" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240916080801_add-field-originalDocumentType', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "RecieptInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "OpeningInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "InventoryIncreased" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "InventoryDecreased" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "ExportInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "ClosingInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ADD "ActionCode" text NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "StockTransactionQuantity" numeric NULL;

ALTER TABLE "sm_Product" ADD "IntitalInventory" numeric NULL;

CREATE INDEX "IX_sm_Cashbook_Transaction_ProjectId" ON "sm_Cashbook_Transaction" ("ProjectId");

ALTER TABLE "sm_Cashbook_Transaction" ADD CONSTRAINT "FK_sm_Cashbook_Transaction_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240916081555_updateTableStockTransaction', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "RecieptInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "OpeningInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "InventoryIncreased" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "InventoryDecreased" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "ExportInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "ClosingInventory" TYPE numeric;

ALTER TABLE "sm_Stock_Transaction" ADD "StockTransactionQuantity" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240916094635_change-type-sm-StockTransaction', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "InitialStockQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240917035523_add-fields-InitialStockQuantity-to-tbl-sm-Product', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "Amount" TO "UnitPrice";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240917072646_rename-field-amount-to-unit-price', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" DROP COLUMN "TypeCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240917093208_remove-cashbook-typecode', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" DROP COLUMN "SubTypeCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240917093257_remove-cashbook-subtypecode', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" RENAME COLUMN "TransactionType" TO "TransactionTypeCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240917094414_rename-cashbook-TransactionTypeCode', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" ALTER COLUMN "VAT" TYPE numeric;
ALTER TABLE "sm_SaleOrders" ALTER COLUMN "VAT" DROP NOT NULL;

ALTER TABLE "sm_SaleOrders" ADD "TotalOtherCost" numeric NULL;

ALTER TABLE "sm_PurchaseOrder" ALTER COLUMN "TotalAmount" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240918090025_add-field-total-other-cost-to-saleOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "QuotationAmount" TO "QuotationUnitPrice";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241002023319_rename-QuotationAmount-to-QuotationUnitPrice-in-PurchaseOrder', '6.0.27');

COMMIT;

---------------------------------------------------------------------------------

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "PaymentStatus" TO "PaymentStatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919021328_rename-paymentStatus-to-paymentStatusCode', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "OtherCost" TO "ListOtherCost";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919021829_rename-otherCost-to-ListOtherCost', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "HalfPayment" TO "ListPayment";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919022512_rename-HalfPayment-to-ListPayment', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "TotalHalfPayment" TO "TotalPaidAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919023058_rename-totalHalfPayment-to-TotalPaidAmount', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "TotalPaymentContinue" TO "TotalRemaningAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919023800_rename-TotalPaymentContinue-to-TotalRemaningAmount', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" RENAME COLUMN "Status" TO "StatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919024350_rename-Status-to-StatusCode', '6.0.27');

COMMIT;


---------------------------------------------------------------------------------------------

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalRecieptInventory" TO "TotalReceiptInventory";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919025025_rename-TotalRecieptInventory-to-TotalReceipeInventory', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "HalfPayment" TO "ListPayment";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919025536_rename-HalfPayment-to-ListPayment-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalHalfPayment" TO "TotalPaidAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919030139_rename-HalfPayment-to-TotalHalfPayment-in-TotalPaidAmount-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalPaymentContinue" TO "TotalRemainingAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919031501_rename-TotalPaymentContinue-to-TotalRemainingAmount-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "Status" TO "StatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919032358_rename-Status-to-StatusCode-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "PaymentStatus" TO "PaymentStatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919032814_rename-PaymentStatus-to-PaymentStatusCode-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "PaymentMethod" TO "PaymentMethodCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919033148_rename-PaymentMethod-to-PaymentMethodCode-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ALTER COLUMN "WareCode" TYPE character varying(64);

ALTER TABLE "sm_PurchaseOrder" ALTER COLUMN "TypeName" TYPE character varying(512);

ALTER TABLE "sm_PurchaseOrder" ALTER COLUMN "TypeCode" TYPE character varying(64);

ALTER TABLE "sm_PurchaseOrder" ALTER COLUMN "OrderCode" TYPE character varying(64);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919071220_character-limit-field-in-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "ReceiveStatusCode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240924021951_add-field-ReceiveStatusCode-to-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "QuotationId" uuid NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240925074943_add-field-QuotationId-to-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ADD "QuotationAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240930042727_add-field-QuotationAmount-to-PurchaseOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "InvoiceRecieptAddress" TO "InvoiceReceiptAddress";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240930075529_rename-InvoiceRecieptAddress-to-InvoiceReceiptAddress-in-PurchaseOrder', '6.0.27');

COMMIT;

-------------------------------------------------------------------------------------------------

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "TotalAmount" TO "TotalLineAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919034629_rename-TotalAmount-to-TotalLineAmount-in-PurchaseOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "ProductName" TYPE character varying(512);

ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "ProductCode" TYPE character varying(64);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919071921_character-limit-field-in-PurchaseOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "DiscountPercent" TYPE numeric;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240921042700_change-type-DiscountPercent-from-int-to-decimal-in-PurchaseOrderItem', '6.0.27');

COMMIT;

-------------------------------------------------------------------------------------------------

START TRANSACTION;

ALTER TABLE "sm_SaleOrderItem" RENAME COLUMN "TotalAmount" TO "TotalLineAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919034949_rename-TotalAmount-to-TotalLineAmount-in-SaleOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrderItem" ALTER COLUMN "Quantity" TYPE numeric;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923045646_change-type-Quantity-from-int-to-decimal-in-SaleOrderItem', '6.0.27');

COMMIT;

---------------------------------------------------------------------------------------------

START TRANSACTION;

ALTER TABLE "sm_Customer" DROP COLUMN "BaoGia";

ALTER TABLE "sm_QuotationItem" ALTER COLUMN "Unit" TYPE character varying(128);

ALTER TABLE "sm_QuotationItem" ALTER COLUMN "Name" TYPE character varying(512);

ALTER TABLE "sm_QuotationItem" ALTER COLUMN "LineNote" TYPE character varying(128);

ALTER TABLE "sm_QuotationItem" ALTER COLUMN "Code" TYPE character varying(64);

ALTER TABLE "sm_Customer" ADD "isQuotation" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919041738_change-type-and-name-field-BaoGia-and-character-limit-fields-in-sm_QuotationItem', '6.0.27');

COMMIT;


--
--
--

START TRANSACTION;

ALTER TABLE "sm_SaleOrders" ALTER COLUMN "WareCode" TYPE character varying(64);

ALTER TABLE "sm_SaleOrders" ALTER COLUMN "TypeName" TYPE character varying(512);

ALTER TABLE "sm_SaleOrders" ALTER COLUMN "TypeCode" TYPE character varying(64);

ALTER TABLE "sm_SaleOrders" ALTER COLUMN "OrderCode" TYPE character varying(64);

ALTER TABLE "sm_SaleOrderItem" ALTER COLUMN "Unit" TYPE character varying(128);

ALTER TABLE "sm_SaleOrderItem" ALTER COLUMN "ProductName" TYPE character varying(512);

ALTER TABLE "sm_SaleOrderItem" ALTER COLUMN "ProductCode" TYPE character varying(64);

ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "Unit" TYPE character varying(128);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240919072723_character-limit-field-in-SaleOrder-and-SaleOrderItem-and-PurchaseOrderItem', '6.0.27');

COMMIT;

--
--
--

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" DROP CONSTRAINT "FK_sm_QuotationItem_sm_Quotation_sm_QuotationId";

DROP INDEX "IX_sm_QuotationItem_sm_QuotationId";

ALTER TABLE "sm_QuotationItem" DROP COLUMN "sm_QuotationId";

ALTER TABLE "sm_Customer" DROP COLUMN "isQuotation";

ALTER TABLE "sm_QuotationItem" ADD "QuotationId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "sm_Customer" ADD "TotalQuotation" integer NOT NULL DEFAULT 0;

CREATE INDEX "IX_sm_QuotationItem_QuotationId" ON "sm_QuotationItem" ("QuotationId");

ALTER TABLE "sm_QuotationItem" ADD CONSTRAINT "FK_sm_QuotationItem_sm_Quotation_QuotationId" FOREIGN KEY ("QuotationId") REFERENCES "sm_Quotation" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240921034314_remove-column-isQuotation-add-column-TotalQuotation-and-add-fk-QuotationId-in-entity-QuotationItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Customer" DROP COLUMN "TotalQuotation";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923015432_remove-column-TotalQuotation', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" DROP COLUMN "TotalExportInventory";

ALTER TABLE "sm_Stock_Transaction" DROP COLUMN "TotalRecieptInventory";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923025008_remove-unused-field', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "ReceiptInventory" TO "ReceiptInventoryQuantity";

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "OpeningInventory" TO "OpeningInventoryQuantity";

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "ClosingInventory" TO "ClosingInventoryQuantity";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923030028_rename-column-sm-StockTransaction', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "ExportInventory" TO "ExportInventoryQuantity";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923031215_rename-column-ExportInventory-to-ExportInventoryQuantity', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "ClosingInventoryAmount" numeric NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "OpeningInventoryAmount" numeric NULL;

ALTER TABLE "sm_Stock_Transaction" ADD "ReceiptInventoryAmout" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923031526_add-columns-to-table-sm-StockTransaction', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" RENAME COLUMN "ReceiptInventoryAmout" TO "ReceiptInventoryAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923031936_rename-column-ReceiptInventoryAmout', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "ExportInventoryAmount" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240923033932_add-column-ExportInventoryAmount', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "IsOrder" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240930025821_add-field-isOrder-to-tbl-smProduct', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "DebtAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241002082251_add-column-DebtAmount-in-entity-customer', '6.0.27');

COMMIT;


---
---
---
START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "OriginalDocumentId" uuid NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241003030853_add-column-OriginalDocumentId-in-entity-CashbookTransaction', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "OriginalDocumentType" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241003034831_add-column-OriginalDocumentType-in-entity-CashbookTransaction', '6.0.27');

COMMIT;


---
---
---
START TRANSACTION;

CREATE TABLE "sm_DebtTransaction" (
    "Id" uuid NOT NULL,
    "EntityId" uuid NOT NULL,
    "EntityCode" character varying(30) NULL,
    "EntityType" character varying(30) NULL,
    "EntityName" character varying(256) NULL,
    "OriginalDocumentId" uuid NULL,
    "OriginalDocumentCode" text NULL,
    "OriginalDocumentType" text NULL,
    "ChangeAmount" numeric NOT NULL,
    "DebtAmount" numeric NOT NULL,
    "Action" character varying(30) NULL,
    "Note" character varying(256) NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_DebtTransaction" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004020850_CreateSmDebtTransactionTable', '6.0.27');

COMMIT;

--
--
--

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" DROP CONSTRAINT "FK_sm_PurchaseOrder_sm_Customer_CustomerId";

ALTER TABLE "sm_PurchaseOrderItem" DROP CONSTRAINT "FK_sm_PurchaseOrderItem_sm_PurchaseOrder_PurchaseOrderId";

ALTER TABLE "sm_PurchaseOrder" DROP CONSTRAINT "PK_sm_PurchaseOrder";

ALTER TABLE "sm_PurchaseOrder" RENAME TO "sm_SalesOrder";

ALTER INDEX "IX_sm_PurchaseOrder_CustomerId" RENAME TO "IX_sm_SalesOrder_CustomerId";

ALTER TABLE "sm_SalesOrder" ADD CONSTRAINT "PK_sm_SalesOrder" PRIMARY KEY ("Id");

ALTER TABLE "sm_PurchaseOrderItem" ADD CONSTRAINT "FK_sm_PurchaseOrderItem_sm_SalesOrder_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "sm_SalesOrder" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_SalesOrder" ADD CONSTRAINT "FK_sm_SalesOrder_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004024619_rename-table-PurchaseOrder-to-SalesOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrderItem" DROP CONSTRAINT "FK_sm_SaleOrderItem_sm_SaleOrders_IdPhieu";

ALTER TABLE "sm_SaleOrders" DROP CONSTRAINT "FK_sm_SaleOrders_mk_DuAn_mk_DuAnId";

ALTER TABLE "sm_SaleOrders" DROP CONSTRAINT "FK_sm_SaleOrders_sm_Supplier_SupplierId";

ALTER TABLE "sm_SaleOrders" DROP CONSTRAINT "PK_sm_SaleOrders";

ALTER TABLE "sm_SaleOrders" RENAME TO "sm_PurchaseOrder";

ALTER INDEX "IX_sm_SaleOrders_SupplierId" RENAME TO "IX_sm_PurchaseOrder_SupplierId";

ALTER INDEX "IX_sm_SaleOrders_mk_DuAnId" RENAME TO "IX_sm_PurchaseOrder_mk_DuAnId";

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "PK_sm_PurchaseOrder" PRIMARY KEY ("Id");

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "FK_sm_PurchaseOrder_mk_DuAn_mk_DuAnId" FOREIGN KEY ("mk_DuAnId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "FK_sm_PurchaseOrder_sm_Supplier_SupplierId" FOREIGN KEY ("SupplierId") REFERENCES "sm_Supplier" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_SaleOrderItem" ADD CONSTRAINT "FK_sm_SaleOrderItem_sm_PurchaseOrder_IdPhieu" FOREIGN KEY ("IdPhieu") REFERENCES "sm_PurchaseOrder" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004024845_rename-table-SaleOrders-to-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" DROP CONSTRAINT "FK_sm_PurchaseOrderItem_sm_Product_ProductId";

ALTER TABLE "sm_PurchaseOrderItem" DROP CONSTRAINT "FK_sm_PurchaseOrderItem_sm_SalesOrder_PurchaseOrderId";

ALTER TABLE "sm_PurchaseOrderItem" DROP CONSTRAINT "PK_sm_PurchaseOrderItem";

ALTER TABLE "sm_PurchaseOrderItem" RENAME TO "sm_SalesOrderItem";

ALTER INDEX "IX_sm_PurchaseOrderItem_PurchaseOrderId" RENAME TO "IX_sm_SalesOrderItem_PurchaseOrderId";

ALTER INDEX "IX_sm_PurchaseOrderItem_ProductId" RENAME TO "IX_sm_SalesOrderItem_ProductId";

ALTER TABLE "sm_SalesOrderItem" ADD CONSTRAINT "PK_sm_SalesOrderItem" PRIMARY KEY ("Id");

ALTER TABLE "sm_SalesOrderItem" ADD CONSTRAINT "FK_sm_SalesOrderItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_SalesOrderItem" ADD CONSTRAINT "FK_sm_SalesOrderItem_sm_SalesOrder_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "sm_SalesOrder" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004025034_rename-table-PurchaseOrderItem-to-SalesOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SaleOrderItem" DROP CONSTRAINT "FK_sm_SaleOrderItem_sm_Product_ProductId";

ALTER TABLE "sm_SaleOrderItem" DROP CONSTRAINT "FK_sm_SaleOrderItem_sm_PurchaseOrder_IdPhieu";

ALTER TABLE "sm_SaleOrderItem" DROP CONSTRAINT "PK_sm_SaleOrderItem";

ALTER TABLE "sm_SaleOrderItem" RENAME TO "sm_PurchaseOrderItem";

ALTER INDEX "IX_sm_SaleOrderItem_ProductId" RENAME TO "IX_sm_PurchaseOrderItem_ProductId";

ALTER INDEX "IX_sm_SaleOrderItem_IdPhieu" RENAME TO "IX_sm_PurchaseOrderItem_IdPhieu";

ALTER TABLE "sm_PurchaseOrderItem" ADD CONSTRAINT "PK_sm_PurchaseOrderItem" PRIMARY KEY ("Id");

ALTER TABLE "sm_PurchaseOrderItem" ADD CONSTRAINT "FK_sm_PurchaseOrderItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_PurchaseOrderItem" ADD CONSTRAINT "FK_sm_PurchaseOrderItem_sm_PurchaseOrder_IdPhieu" FOREIGN KEY ("IdPhieu") REFERENCES "sm_PurchaseOrder" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004025238_rename-table-SaleOrderItem-to-PurchaseOrderItem', '6.0.27');

COMMIT;

--
--
--


START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" DROP CONSTRAINT "FK_sm_SalesOrderItem_sm_SalesOrder_PurchaseOrderId";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "TotalLineAmount" TO "LineAmount";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "PurchaseOrderId" TO "SalesOrderId";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "Amount" TO "UnitPrice";

ALTER INDEX "IX_sm_SalesOrderItem_PurchaseOrderId" RENAME TO "IX_sm_SalesOrderItem_SalesOrderId";

ALTER TABLE "sm_SalesOrderItem" ADD CONSTRAINT "FK_sm_SalesOrderItem_sm_SalesOrder_SalesOrderId" FOREIGN KEY ("SalesOrderId") REFERENCES "sm_SalesOrder" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004040310_refactor-sm_SalesOrderItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ADD "DiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" ADD "DiscountType" character varying(10) NULL;

ALTER TABLE "sm_SalesOrderItem" ADD "Note" character varying(256) NULL;

ALTER TABLE "sm_SalesOrderItem" ADD "VATAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" ADD "VATPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" ADD "VATableAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004040651_refactor-sm_SalesOrderItem-add-fields', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ALTER COLUMN "Quantity" TYPE numeric;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004041139_ref-sm_SalesOrderItem-change-Quantity-to-decimal', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ADD "LineNo" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004041227_refactor-sm_SalesOrderItem-add-LineNo', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "DeliveryFee" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrder" ADD "DiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrder" ADD "DiscountPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrder" ADD "DiscountType" character varying(10) NULL;

ALTER TABLE "sm_SalesOrder" ADD "Total" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrder" ADD "VATAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004064034_add-sm_SalesOrder-fields', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" DROP COLUMN "TotalAmount";

ALTER TABLE "sm_SalesOrder" DROP COLUMN "TotalDiscountsAccount";

ALTER TABLE "sm_SalesOrder" DROP COLUMN "TotalPaidAmount";

ALTER TABLE "sm_SalesOrder" RENAME COLUMN "TotalRemainingAmount" TO "RemainingAmount";

ALTER TABLE "sm_SalesOrder" RENAME COLUMN "TotalReceiptInventory" TO "PaidAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004064409_ref-sm_SalesOrder-fields', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ADD "AfterLineDiscountGoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" ADD "GoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" ADD "SalesOrderDiscountAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241004074144_add-sm_SalesOrderItem-GoodsAmount-SalesOrderDiscountAmount-AfterLineDiscountGoodsAmount', '6.0.27');

COMMIT;

--
--
--


START TRANSACTION;

ALTER TABLE "sm_SalesOrder" RENAME COLUMN "ReceiveStatusCode" TO "ExportStatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005020357_rename-sm_SalesOrder-ReceiveStatusCode', '6.0.27');

COMMIT;

--
--
--

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ADD "IsVATApplied" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "sm_SalesOrderItem" ADD "VATCode" text NULL;

ALTER TABLE "sm_Product" ADD "ExportVATCode" character varying(32) NULL;

ALTER TABLE "sm_Product" ADD "ExportVATPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Product" ADD "ImportVATCode" character varying(32) NULL;

ALTER TABLE "sm_Product" ADD "ImportVATPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Product" ADD "IsVATApplied" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005050141_add-SalesOrderItem-Product-VAT-fields', '6.0.27');

COMMIT;

--
--
--

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "DiscountType" TO "UnitPriceDiscountType";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "DiscountPercent" TO "UnitPriceDiscountPercent";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "DiscountAmount" TO "UnitPriceDiscountAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005071913_rename-SalesOrder-Discount', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "DiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrder" ADD "DiscountPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrder" ADD "DiscountType" character varying(10) NULL;

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "TotalLineAmount" TO "LineAmount";

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "Amout" TO "UnitPrice";

ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "DiscountPercent" TYPE numeric;
UPDATE "sm_PurchaseOrderItem" SET "DiscountPercent" = 0.0 WHERE "DiscountPercent" IS NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "DiscountPercent" SET NOT NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "DiscountPercent" SET DEFAULT 0.0;

UPDATE "sm_PurchaseOrderItem" SET "UnitPrice" = 0.0 WHERE "UnitPrice" IS NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "UnitPrice" SET NOT NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "UnitPrice" SET DEFAULT 0.0;

UPDATE "sm_PurchaseOrderItem" SET "Quantity" = 0.0 WHERE "Quantity" IS NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "Quantity" SET NOT NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "Quantity" SET DEFAULT 0.0;

UPDATE "sm_PurchaseOrderItem" SET "LineAmount" = 0.0 WHERE "LineAmount" IS NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "LineAmount" SET NOT NULL;
ALTER TABLE "sm_PurchaseOrderItem" ALTER COLUMN "LineAmount" SET DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "DiscountPercent" TO "UnitPriceDiscountPercent";

ALTER TABLE "sm_PurchaseOrderItem" ADD "AfterLineDiscountGoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "DiscountedUnitPrice" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "GoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "IsProductVATApplied" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "sm_PurchaseOrderItem" ADD "LineNo" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "SalesOrderDiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "UnitPriceDiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "UnitPriceDiscountType" character varying(10) NULL;

ALTER TABLE "sm_PurchaseOrderItem" ADD "VATAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "VATCode" text NULL;

ALTER TABLE "sm_PurchaseOrderItem" ADD "VATPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_PurchaseOrderItem" ADD "VATableAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005084841_ref-po-item-table', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "VAT" TO "VATAmount";

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalAmount" TO "Total";

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "Reason" TO "PurchaseReason";

ALTER TABLE "sm_PurchaseOrder" ADD "Reference" character varying(128) NULL;

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalRemaningAmount" TO "RemainingAmount";

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "TotalPaidAmount" TO "PaidAmount";

ALTER TABLE "sm_PurchaseOrder" RENAME COLUMN "ReceiveInventoryStatus" TO "ImportStatusCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005091825_ref-PO-table', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" DROP CONSTRAINT "FK_sm_PurchaseOrderItem_sm_PurchaseOrder_IdPhieu";

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "SalesOrderDiscountAmount" TO "OrderDiscountAmount";

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "SalesOrderDiscountAmount" TO "OrderDiscountAmount";

ALTER TABLE "sm_PurchaseOrderItem" RENAME COLUMN "IdPhieu" TO "PurchaseOrderId";

ALTER INDEX "IX_sm_PurchaseOrderItem_IdPhieu" RENAME TO "IX_sm_PurchaseOrderItem_PurchaseOrderId";

ALTER TABLE "sm_PurchaseOrderItem" ADD CONSTRAINT "FK_sm_PurchaseOrderItem_sm_PurchaseOrder_PurchaseOrderId" FOREIGN KEY ("PurchaseOrderId") REFERENCES "sm_PurchaseOrder" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005104813_rename-PurchaseOrder-fields-and-FK', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "IsDebt" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241005102607_add-col-IsDebt-in-entity-CashbookTransaction', '6.0.27');

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "DiscountReason" text NULL;

ALTER TABLE "sm_SalesOrder" ADD "TotalDiscountAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_SalesOrderItem" RENAME COLUMN "IsVATApplied" TO "IsProductVATApplied";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241008034415_add-SO-DiscountReason-TotalDiscountAmount', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Return_Order" (
    "Id" uuid NOT NULL,
    "OrderCode" character varying(64) NULL,
    "ReasonCode" character varying(128) NULL,
    "EntityId" uuid NOT NULL,
    "EntityCode" character varying(64) NULL,
    "EntityName" text NULL,
    "EntityTypeCode" character varying(64) NULL,
    "EntityTypeName" text NULL,
    "OriginalDocumentId" uuid NOT NULL,
    "OriginalDocumentCode" character varying(64) NULL,
    "ListPayment" jsonb NULL,
    "RefundSubTotal" numeric NOT NULL,
    "PaidAmount" numeric NULL,
    "RemainingRefundAmount" numeric NOT NULL,
    "Note" character varying(128) NULL,
    "StatusCode" text NULL,
    "RefundStatusCode" text NULL,
    "CancelledOnDate" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Return_Order" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_Return_Order_Item" (
    "Id" uuid NOT NULL,
    "ProductCode" text NULL,
    "ProductName" text NULL,
    "Unit" text NULL,
    "ReturnedQuantity" numeric NOT NULL,
    "InitialQuantity" numeric NOT NULL,
    "RemainingQuantity" numeric NOT NULL,
    "InitialUnitPrice" numeric NOT NULL,
    "ReturnedUnitPrice" numeric NOT NULL,
    "LineAmount" numeric NOT NULL,
    "ProductId" uuid NOT NULL,
    "ReturnOrderId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Return_Order_Item" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Return_Order_Item_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Return_Order_Item_sm_Return_Order_ReturnOrderId" FOREIGN KEY ("ReturnOrderId") REFERENCES "sm_Return_Order" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Return_Order_Item_ProductId" ON "sm_Return_Order_Item" ("ProductId");

CREATE INDEX "IX_sm_Return_Order_Item_ReturnOrderId" ON "sm_Return_Order_Item" ("ReturnOrderId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241018044238_add-table-OrderReturn-and-OrderReturnItem', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ALTER COLUMN "RemainingRefundAmount" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241019045512_change-type-RemainingRefundAmount-from-null-to_nullable', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "IsReturned" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241021071413_add-field-isReturned-to-purchaseOrderTable', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "IsReturned" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241021071827_add-field-isReturned-to-salesOrderTable', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ADD "WareCode" character varying(64) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241022081004_add-field-WareCode-to-ReturnOrderTable', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "InitialStockQuantity" numeric NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241024023630_add-field-InitialStockQuantity-to-StockTransactionTable', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ADD "RemainingQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241024031622_add-field-RemainingQuantity-to-PurchaseOrderItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrderItem" ADD "IsReturnedItem" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "sm_PurchaseOrderItem" ADD "IsReturnedItem" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241025021538_add-field-IsReturnedItem-to-PurchaseOrderItem-and-SalesOrderItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ALTER COLUMN "EntityTypeName" TYPE character varying(128);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241025040034_add-limit-character-entityTypeName-in-OrderReturnModel', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ALTER COLUMN "EntityName" TYPE character varying(128);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241025040620_add-limit-character-entityName-in-OrderReturnModel', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrderItem" ADD "Note" character varying(256) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241028042036_add-field-Note-to-PurchaseOrderItem', '6.0.27');

COMMIT;

--
--
--


START TRANSACTION;

CREATE TABLE "sm_InventoryNote" (
    "Id" uuid NOT NULL,
    "Code" character varying(64) NULL,
    "EntityId" uuid NOT NULL,
    "EntityCode" character varying(64) NULL,
    "EntityName" character varying(256) NULL,
    "EntityTypeCode" character varying(64) NULL,
    "EntityTypeName" character varying(128) NULL,
    "OriginalDocumentId" uuid NULL,
    "OriginalDocumentType" character varying(64) NULL,
    "OriginalDocumentCode" character varying(64) NULL,
    "TransactionTypeCode" character varying(64) NULL,
    "TransactionTypeName" character varying(128) NULL,
    "TransactionDate" timestamp without time zone NULL,
    "InventoryCode" character varying(64) NULL,
    "InventoryName" character varying(256) NULL,
    "ProjectId" uuid NULL,
    "ProjectName" character varying(256) NULL,
    "Note" character varying(512) NULL,
    "TypeCode" character varying(64) NULL,
    "StatusCode" character varying(64) NULL,
    "StatusName" character varying(128) NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_InventoryNote" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_InventoryNoteItem" (
    "Id" uuid NOT NULL,
    "ProductId" uuid NOT NULL,
    "LineNumber" integer NOT NULL,
    "ProductCode" character varying(64) NULL,
    "ProductName" character varying(512) NULL,
    "Unit" character varying(128) NULL,
    "Quantity" numeric NOT NULL,
    "Note" character varying(128) NULL,
    "InventoryNoteId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_InventoryNoteItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_InventoryNoteItem_sm_InventoryNote_InventoryNoteId" FOREIGN KEY ("InventoryNoteId") REFERENCES "sm_InventoryNote" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_InventoryNoteItem_InventoryNoteId" ON "sm_InventoryNoteItem" ("InventoryNoteId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241023022948_add-entity-InventoryNote-and-InventoryNoteItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

UPDATE "mk_DuAn" SET "TongHopThu" = 0.0 WHERE "TongHopThu" IS NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopThu" SET NOT NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopThu" SET DEFAULT 0.0;

UPDATE "mk_DuAn" SET "TongHopChi" = 0.0 WHERE "TongHopChi" IS NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopChi" SET NOT NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TongHopChi" SET DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241028091409_change-field-TongHopThu-TongHopChi-non-nullable-and-default-values-0M', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

UPDATE "sm_Cashbook_Transaction" SET "Amount" = 0.0 WHERE "Amount" IS NULL;
ALTER TABLE "sm_Cashbook_Transaction" ALTER COLUMN "Amount" SET NOT NULL;
ALTER TABLE "sm_Cashbook_Transaction" ALTER COLUMN "Amount" SET DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241028093326_change-field-Amount-non-nullable-and-default-values-0M', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

DROP TABLE "mk_ThuChi";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241029025059_remove-api-ThuChi', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Quotation" DROP CONSTRAINT "FK_sm_Quotation_mk_DuAn_mk_DuAnId";

DROP INDEX "IX_sm_Quotation_mk_DuAnId";

ALTER TABLE "sm_Quotation" DROP COLUMN "mk_DuAnId";

CREATE INDEX "IX_sm_Quotation_ProjectId" ON "sm_Quotation" ("ProjectId");

ALTER TABLE "sm_Quotation" ADD CONSTRAINT "FK_sm_Quotation_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241029034737_add-fk-du-an-quotation', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" DROP CONSTRAINT "FK_sm_PurchaseOrder_mk_DuAn_mk_DuAnId";

DROP INDEX "IX_sm_PurchaseOrder_mk_DuAnId";

ALTER TABLE "sm_PurchaseOrder" DROP COLUMN "mk_DuAnId";

CREATE INDEX "IX_sm_InventoryNote_ProjectId" ON "sm_InventoryNote" ("ProjectId");

ALTER TABLE "sm_InventoryNote" ADD CONSTRAINT "FK_sm_InventoryNote_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241029050429_add-fk-du-an-inventory-note', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

CREATE TABLE "IdmRight" (
   "Id" uuid NOT NULL,
   "Name" character varying(128) NULL,
   "Code" character varying(64) NULL,
   "Description" character varying(128) NULL,
   "GroupCode" text NULL,
   "GroupName" text NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_IdmRight" PRIMARY KEY ("Id")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241029071248_add-table-idmRight-for-module-Authorize', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "IdmRightMapRole" (
    "Id" uuid NOT NULL,
    "RoleCode" text NULL,
    "RightCode" text NULL,
    "RightId" uuid NULL,
    "RoleId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NULL,
    CONSTRAINT "PK_IdmRightMapRole" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_IdmRightMapRole_idm_Role_RoleId" FOREIGN KEY ("RoleId") REFERENCES "idm_Role" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_IdmRightMapRole_IdmRight_RightId" FOREIGN KEY ("RightId") REFERENCES "IdmRight" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_IdmRightMapRole_RightId" ON "IdmRightMapRole" ("RightId");

CREATE INDEX "IX_IdmRightMapRole_RoleId" ON "IdmRightMapRole" ("RoleId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241030035247_add-table-idmRightMapRole', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Return_Order_Item" ADD "LineNo" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241030041639_add-field-lineNo-to-OrderReturnItem', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

UPDATE "IdmRightMapRole" SET "CreatedOnDate" = TIMESTAMP '-infinity' WHERE "CreatedOnDate" IS NULL;
ALTER TABLE "IdmRightMapRole" ALTER COLUMN "CreatedOnDate" SET NOT NULL;
ALTER TABLE "IdmRightMapRole" ALTER COLUMN "CreatedOnDate" SET DEFAULT TIMESTAMP '-infinity';

ALTER TABLE "IdmRightMapRole" ADD "CreatedByUserId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "IdmRightMapRole" ADD "CreatedByUserName" text NULL;

ALTER TABLE "IdmRightMapRole" ADD "LastModifiedByUserId" uuid NULL;

ALTER TABLE "IdmRightMapRole" ADD "LastModifiedByUserName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241031043842_update-base-table-service', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "CustomerGroupCode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241104071642_add-field-CustomerGroupCode-in-sm_KhachHang', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Supplier" ADD "SupplierGroupCode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241104072450_add-field-SupplierGroupCode-in-mk_NhaCungCap', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "ExpenseAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Customer" ADD "OrderCount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241105023442_add-field-ExpenseAmount-and-OrderCount-into-sm_KhachHang', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Supplier" ADD "ExpenseAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Supplier" ADD "OrderCount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241105024128_add-field-ExpenseAmount-and-OrderCount-into-sm_Supplier', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryNote" ADD "TotalQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241106025807_add-col-TotalQuantity-in-entity-InventoryNote', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "TotalQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241108024816_add-field-TotalQuantity-into-SalesOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "CancelledOnDate" timestamp without time zone NULL;

ALTER TABLE "sm_SalesOrder" ADD "CancelledReason" character varying(128) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241109081632_add-field-CancelledOnDate-and-CancelledReason-into-SalesOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "OtherCostAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241110014801_add-field-OtherCostAmount-into-SalesOrder', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "TotalQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241108030731_add-field-TotalQuantity-into-PurchaseOrder', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "CancelledOnDate" timestamp without time zone NULL;

ALTER TABLE "sm_PurchaseOrder" ADD "CancelledReason" character varying(128) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241109080914_add-field-CancelledOnDate-and-CancelledReason-into-PurchaseOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" ADD "IsProductVATApplied" boolean NOT NULL DEFAULT FALSE;

ALTER TABLE "sm_QuotationItem" ADD "VATAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_QuotationItem" ADD "VATCode" text NULL;

ALTER TABLE "sm_QuotationItem" ADD "VATPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_QuotationItem" ADD "VATableAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112021745_add-columns-for-tax-purposes', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" ADD "ProductId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112035019_add-column-ProductId-in-entity-QuotationItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" RENAME COLUMN "VATableAmount" TO "LineVATableAmount";

ALTER TABLE "sm_QuotationItem" RENAME COLUMN "VATPercent" TO "LineVATPercent";

ALTER TABLE "sm_QuotationItem" RENAME COLUMN "VATCode" TO "LineVATCode";

ALTER TABLE "sm_QuotationItem" RENAME COLUMN "VATAmount" TO "LineVATAmount";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112040725_rename-VAT-properties-in-QuotationItem-for-clarity', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" RENAME COLUMN "DiscountAmount" TO "UnitPriceDiscountAmount";

ALTER TABLE "sm_QuotationItem" ADD "GoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_QuotationItem" ADD "UnitPriceDiscountPercent" numeric NULL;

ALTER TABLE "sm_QuotationItem" ADD "UnitPriceDiscountType" character varying(10) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112043943_add-cols-UnitPriceDiscountType-UnitPriceDiscountPercent-GoodsAmount-and-rename-col-DiscountAmount', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" ADD "AfterLineDiscountGoodsAmount" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_QuotationItem" ADD "OrderDiscountAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112044622_add-cols-AfterLineDiscountGoodsAmount-OrderDiscountAmount-in-entity-QuotationItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Quotation" RENAME COLUMN "VatAmount" TO "TotalVatAmount";

ALTER TABLE "sm_Quotation" ADD "DiscountPercent" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Quotation" ADD "DiscountType" character varying(10) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241112045658_add-cols-DiscountType-DiscountPercent-rename-col-VatAmount', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_QuotationItem" ALTER COLUMN "UnitPrice" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241113021346_make-UnitPrice-nullable-in-QuotationItem-class', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "QuotationCode" text NULL;

ALTER TABLE "sm_SalesOrder" ADD "SellableQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241113144720_add-field-QuotationCode-and-SellableQuantity-into-SalesOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" DROP COLUMN "SellableQuantity";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241113150411_remove-field-SellableQuantity-in-SalesOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "SellableQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Product" ADD "WareCode" character varying(32) NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241115025707_add-field-WareCode-and-SellableQuantity-into-sm_Product', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" DROP COLUMN "WareCode";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241118092628_remove-field-WareCode-in-sm_Product', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "ListWareCodes" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241118093455_add-field-ListWareCode-in-sm_Product', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "SellableQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241116050522_add-field-SellableQuantity-into-StockTransaction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ADD "OriginalDocumentId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241115071510_add-field-OriginalDocumentId-into-StockTransaction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Stock_Transaction" ALTER COLUMN "OriginalDocumentId" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241115072058_change-type-field-OriginalDocumentId-into-StockTransaction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_InventoryCheckNote" (
    "Id" uuid NOT NULL,
    "WareCode" character varying(64) NULL,
    "OrderCode" character varying(64) NULL,
    "Note" text NULL,
    "Tag" text NULL,
    "StatusCode" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_InventoryCheckNote" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_InventoryCheckNoteItems" (
    "Id" uuid NOT NULL,
    "ProductCode" text NULL,
    "ProductName" text NULL,
    "Unit" text NULL,
    "LineNo" integer NOT NULL,
    "ActualInventory" numeric NOT NULL,
    "RealityInventory" numeric NOT NULL,
    "DifferenceQuantity" numeric NOT NULL,
    "NoteInventory" text NULL,
    "ReasonInventory" text NULL,
    "DifferenceType" text NULL,
    "ProductId" uuid NOT NULL,
    "CheckInventoryNoteId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_InventoryCheckNoteItems" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_InventoryCheckNoteItems_sm_InventoryCheckNote_CheckInventoryNoteId" FOREIGN KEY ("CheckInventoryNoteId") REFERENCES "sm_InventoryCheckNote" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_InventoryCheckNoteItems_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_InventoryCheckNoteItems_CheckInventoryNoteId" ON "sm_InventoryCheckNoteItems" ("CheckInventoryNoteId");

CREATE INDEX "IX_sm_InventoryCheckNoteItems_ProductId" ON "sm_InventoryCheckNoteItems" ("ProductId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241119092436_create-table-InventoryCheckNote-and-InventoryCheckNoteItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryCheckNote" ADD "CheckDate" timestamp without time zone NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241120014957_add-field-CheckDate-into-InventoryCheckNote', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PurchaseOrder" ADD "ProjectId" uuid NULL;

CREATE INDEX "IX_sm_PurchaseOrder_ProjectId" ON "sm_PurchaseOrder" ("ProjectId");

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "FK_sm_PurchaseOrder_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241120063615_add-field-ProjectId-into-PurchaseOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryCheckNoteItems" RENAME COLUMN "RealityInventory" TO "RecordedQuantity";

ALTER TABLE "sm_InventoryCheckNoteItems" RENAME COLUMN "ActualInventory" TO "ActualQuantity";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241120103439_rename-field-in-InventoryCheckNoteItems', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE INDEX "IX_sm_Quotation_CustomerId" ON "sm_Quotation" ("CustomerId");

ALTER TABLE "sm_Quotation" ADD CONSTRAINT "FK_sm_Quotation_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241121043716_add-fk-relationship-to-sm_Quotation-class', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" ALTER COLUMN "ProductGroupId" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241121101102_make-ProductGroupId-nullable-in-sm_Product-class', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "ProjectId" uuid NULL;

CREATE INDEX "IX_sm_SalesOrder_ProjectId" ON "sm_SalesOrder" ("ProjectId");

ALTER TABLE "sm_SalesOrder" ADD CONSTRAINT "FK_sm_SalesOrder_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241123074331_add-field-ProjectId-into-SalesOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ADD "ProjectId" uuid NULL;

CREATE INDEX "IX_sm_Return_Order_ProjectId" ON "sm_Return_Order" ("ProjectId");

ALTER TABLE "sm_Return_Order" ADD CONSTRAINT "FK_sm_Return_Order_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241123080655_add-field-ProjectId-into-OrderReturn', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryCheckNote" ADD "BalancedByUserName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241126080402_add-field-BalanceByUserName-into-InventoryCheckNote', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "WareName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241127024742_add-field-WardName-into-SalesOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_WarehouseTransferNote" (
    "Id" uuid NOT NULL,
    "TransferNoteCode" character varying(64) NULL,
    "ExportWarehouseCode" character varying(64) NULL,
    "ImportWarehouseCode" character varying(64) NULL,
    "TransferredOnDate" timestamp without time zone NULL,
    "StatusCode" text NULL,
    "Note" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_WarehouseTransferNote" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_WarehouseTransferNoteItem" (
    "Id" uuid NOT NULL,
    "LineNo" integer NOT NULL,
    "Quantity" numeric NOT NULL,
    "LineNote" text NULL,
    "ProductId" uuid NOT NULL,
    "TransferNoteID" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_WarehouseTransferNoteItem" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_WarehouseTransferNoteItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_WarehouseTransferNoteItem_sm_WarehouseTransferNote_Trans~" FOREIGN KEY ("TransferNoteID") REFERENCES "sm_WarehouseTransferNote" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_WarehouseTransferNoteItem_ProductId" ON "sm_WarehouseTransferNoteItem" ("ProductId");

CREATE INDEX "IX_sm_WarehouseTransferNoteItem_TransferNoteID" ON "sm_WarehouseTransferNoteItem" ("TransferNoteID");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241127045203_create-table-WarehouseTransferNote-and-WarehouseTransferNoteItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_WarehouseTransferNoteItem" ADD "ProductCode" text NULL;

ALTER TABLE "sm_WarehouseTransferNoteItem" ADD "ProductName" text NULL;

ALTER TABLE "sm_WarehouseTransferNoteItem" ADD "Unit" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241127074338_add-field-ProductCode-ProductName-Unit-into-WarehouseTransferNote', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryCheckNote" ADD "WareName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241202071144_add-field-wareName-into-InventoryCheckNote', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_WarehouseTransferNote" ADD "ExportWarehouseName" text NULL;

ALTER TABLE "sm_WarehouseTransferNote" ADD "ImportWarehouseName" text NULL;

ALTER TABLE "sm_WarehouseTransferNote" ADD "TransferredByUserName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241202072305_add-field-importWarehouseName-ExportWarehouseName-TransferredByUserName-into-InventoryCheckNote', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Bom" (
    "Id" uuid NOT NULL,
    "ProductId" uuid NOT NULL,
    "SubTotalAmount" numeric NOT NULL,
    "TotalAmount" numeric NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Bom" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Bom_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_Materials" (
    "Id" uuid NOT NULL,
    "LineNumber" integer NOT NULL,
    "MaterialId" uuid NOT NULL,
    "MaterialCode" uuid NOT NULL,
    "MaterialName" text NULL,
    "MaterialUnit" text NULL,
    "LineVatPercent" numeric NOT NULL,
    "LineVatAmount" numeric NOT NULL,
    "Quantity" numeric NOT NULL,
    "UnitPrice" numeric NOT NULL,
    "LineAmount" numeric NOT NULL,
    "BomId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_Materials" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Materials_sm_Bom_BomId" FOREIGN KEY ("BomId") REFERENCES "sm_Bom" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Bom_ProductId" ON "sm_Bom" ("ProductId");

CREATE INDEX "IX_sm_Materials_BomId" ON "sm_Materials" ("BomId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241202044139_add-entities-sm_Bom-sm_Materials', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Materials" ALTER COLUMN "MaterialCode" TYPE text;
ALTER TABLE "sm_Materials" ALTER COLUMN "MaterialCode" DROP NOT NULL;

ALTER TABLE "sm_Bom" ADD "TotalQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Bom" ADD "TotalVatAmount" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241202072025_add-cols-TotalVatAmouunt-TotalQuantity-in-entity-sm_Bom', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Bom" ADD "Code" text NULL;

ALTER TABLE "sm_Bom" ADD "ListOtherCost" jsonb NULL;

ALTER TABLE "sm_Bom" ADD "Note" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241204064117_add-new-properties-Note-Code-and-ListOtherCost-in-entity-sm_Bom', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Bom" ADD "TotalOtherExpenses" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241205014220_add-TotalOtherExpenses-property-to-sm_Bom-class', '6.0.27');

COMMIT;

---
---
---
START TRANSACTION;

ALTER TABLE "sm_Customer" ADD "PersonInCharge" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241206045109_add-field-PersonInCharge-into-sm_Customer', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryNote" ALTER COLUMN "EntityId" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241210072621_make-EntityId-in-InventoryNote-nullable', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "mk_DuAn" ALTER COLUMN "TenDuAn" TYPE character varying(200);
UPDATE "mk_DuAn" SET "TenDuAn" = '' WHERE "TenDuAn" IS NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TenDuAn" SET NOT NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "TenDuAn" SET DEFAULT '';

ALTER TABLE "mk_DuAn" ALTER COLUMN "MaDuAn" TYPE character varying(100);
UPDATE "mk_DuAn" SET "MaDuAn" = '' WHERE "MaDuAn" IS NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "MaDuAn" SET NOT NULL;
ALTER TABLE "mk_DuAn" ALTER COLUMN "MaDuAn" SET DEFAULT '';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241212032441_required-notnull-maxlength-in-entity-mk_DuAn', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "PurposeName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241217020608_add-col-PurposeName-in-entity-sm_Cashbook_Transaction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_ProductInventory" (
   "Id" uuid NOT NULL,
   "WarehouseCode" character varying(64) NULL,
   "SellableQuantity" numeric NOT NULL,
   "ProductId" uuid NOT NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   CONSTRAINT "PK_sm_ProductInventory" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_ProductInventory_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE                                 
);

CREATE INDEX "IX_sm_ProductInventory_ProductId" ON "sm_ProductInventory" ("ProductId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20241218013724_create-table-sm_ProductInventory', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Customer" RENAME COLUMN "Type" TO "CustomerType";

ALTER TABLE "sm_Customer" ADD "CustomerSource" text[] NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250214044144_add_field_CustomerSource_and_rename_filed_CustomerField', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Customer" ALTER COLUMN "CustomerType" TYPE text;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250214074259_rename_type_CustomerType', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Customer" DROP COLUMN "PersonInCharge";

ALTER TABLE "sm_Customer" ADD "ListPersonInCharge" text[] NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250218022940_rename_field_PersonInCharge_and_change_type_PersonalInCharge', '6.0.27');

COMMIT

---
---
---

START TRANSACTION;

ALTER TABLE "sm_LichSuChamSoc" ADD "CustomerServiceContent" text NULL;

ALTER TABLE "sm_LichSuChamSoc" ADD "DateRange" timestamp without time zone[] NULL;

ALTER TABLE "sm_LichSuChamSoc" ADD "ParticipantPersonId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "sm_LichSuChamSoc" ADD "Priority" text NULL;

ALTER TABLE "sm_LichSuChamSoc" ADD "PriorityColor" text NULL;

ALTER TABLE "sm_LichSuChamSoc" ADD "ProjectId" uuid NULL;

ALTER TABLE "sm_LichSuChamSoc" ADD "StatusCode" text NULL;

CREATE INDEX "IX_sm_LichSuChamSoc_ProjectId" ON "sm_LichSuChamSoc" ("ProjectId");

ALTER TABLE "sm_LichSuChamSoc" ADD CONSTRAINT "FK_sm_LichSuChamSoc_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250218043228_ref_table_LichSuChamSoc', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_LichSuChamSoc" DROP COLUMN "ParticipantPersonId";

ALTER TABLE "sm_LichSuChamSoc" ADD "Participants" text[] NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250218074928_rename_field_in_sm_LichSuChamSoc', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_CustomerServiceComment" (
    "Id" uuid NOT NULL,
    "Content" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_CustomerServiceComment" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_CustomerServiceComment_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_CustomerServiceComment_CreatedByUserId" ON "sm_CustomerServiceComment" ("CreatedByUserId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250219064501_add_table_sm_CustomerServiceComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" ADD "CustomerServiceId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_CustomerServiceComment_CustomerServiceId" ON "sm_CustomerServiceComment" ("CustomerServiceId");

ALTER TABLE "sm_CustomerServiceComment" ADD CONSTRAINT "FK_sm_CustomerServiceComment_sm_LichSuChamSoc_CustomerServiceId" FOREIGN KEY ("CustomerServiceId") REFERENCES "sm_LichSuChamSoc" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250219070257_add_field_FK_CustomerServiceId_into_CustomerServiceComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" ADD "IsSystemLog" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250219082335_add_field_isSystemLog_into_CustomerServiceComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" ALTER COLUMN "CustomerServiceId" DROP NOT NULL;

ALTER TABLE "sm_CustomerServiceComment" ADD "CustomerId" uuid NULL;

CREATE INDEX "IX_sm_CustomerServiceComment_CustomerId" ON "sm_CustomerServiceComment" ("CustomerId");

ALTER TABLE "sm_CustomerServiceComment" ADD CONSTRAINT "FK_sm_CustomerServiceComment_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250220042347_rename_type_field_CustomerServiceId_and_add_field_CustomerId', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" ADD "CustomerType" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250220065837_add_field_CustomerType_into_sm_CustomerServiceComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_LichSuChamSoc" ADD "Code" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250224012447_add_field_code_into_sm_LichSuChamSoc', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_LichSuChamSoc" ADD "Type" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250224022759_add_field_Type_into_sm_LichSuChamSoc', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_EInvoice" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "SellerName" text NULL,
    "SellerTaxCode" text NULL,
    "SellerAddress" text NULL,
    "SellerPhoneNumber" text NULL,
    "SellerBankAccount" text NULL,
    "SellerBankName" text NULL,
    "BuyerName" text NULL,
    "BuyerTaxCode" text NULL,
    "BuyerAddress" text NULL,
    "BuyerPhoneNumber" text NULL,
    "PaymentMethodName" text NULL,
    "BuyerBankAccount" text NULL,
    "BuyerBankName" text NULL,
    "Note" character varying(400) NULL,
    "PaymentStatusCode" character varying(64) NULL,
    "PaymentStatusName" character varying(64) NULL,
    "PaymentStatusColor" character varying(64) NULL,
    "TotalBeforeVatAmount" numeric NOT NULL,
    "TotalVatAmount" numeric NOT NULL,
    "TotalAmount" numeric NOT NULL,
    "TotalAmountInWords" text NULL,
    "PaidAmount" numeric NOT NULL,
    "StillInDebtAmount" numeric NOT NULL,
    "ListOfPaymentHistory" jsonb NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_EInvoice" PRIMARY KEY ("Id")
);

CREATE TABLE "sm_EInvoiceItems" (
    "Id" uuid NOT NULL,
    "LineNumber" integer NOT NULL,
    "Name" character varying(256) NULL,
    "Unit" text NULL,
    "Quantity" numeric NOT NULL,
    "UnitPrice" numeric NOT NULL,
    "LineAmount" numeric NOT NULL,
    "VatPercent" numeric NOT NULL,
    "VatAmount" numeric NOT NULL,
    "sm_EInvoiceId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_EInvoiceItems" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_EInvoiceItems_sm_EInvoice_sm_EInvoiceId" FOREIGN KEY ("sm_EInvoiceId") REFERENCES "sm_EInvoice" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_EInvoiceVatAnalytics" (
    "Id" uuid NOT NULL,
    "Synthetic" text NULL,
    "BeforeVatAmount" numeric NOT NULL,
    "VatAmount" numeric NOT NULL,
    "TotalPaymentAmount" numeric NOT NULL,
    "EInvoiceId" uuid NOT NULL,
    "sm_EInvoiceId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    CONSTRAINT "PK_sm_EInvoiceVatAnalytics" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_EInvoiceVatAnalytics_sm_EInvoice_sm_EInvoiceId" FOREIGN KEY ("sm_EInvoiceId") REFERENCES "sm_EInvoice" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_EInvoiceItems_sm_EInvoiceId" ON "sm_EInvoiceItems" ("sm_EInvoiceId");

CREATE INDEX "IX_sm_EInvoiceVatAnalytics_sm_EInvoiceId" ON "sm_EInvoiceVatAnalytics" ("sm_EInvoiceId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250303032638_Add-classes-for-handling-invoices', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Supplier" ADD "ListAccountBanking" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250304034843_add_field_listAccountBanking_into_mk_NhaCungCap', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Customer" RENAME COLUMN "NhuCauBanDau" TO "PaymentMethod";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250305064921_add_field_paymentMehthod_into_customer', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Supplier" ADD "PaymentMethod" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250306034722_add_field_paymentMethod_into_mk_NhaCungCap', '6.0.27');

COMMIT;

--- Add themeConfigs ---
START TRANSACTION;

ALTER TABLE "idm_User" ADD "ThemeConfigs" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250307033605_add-field-themeConfig-to-tbl-idmUser', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "Barcode" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250310012424_add_field_barcode_into_product_table', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ALTER COLUMN "IsActive" TYPE text;
ALTER TABLE "sm_Cashbook_Transaction" ALTER COLUMN "IsActive" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250310075509_change-type-isActive-smCashbookTransaction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Construction" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Name" text NULL,
    "Address" text NULL,
    "CustomerId" uuid NOT NULL,
    "StartDate" timestamp without time zone NOT NULL,
    "EndDate" timestamp without time zone NOT NULL,
    "Note" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Construction" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Construction_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Construction_CustomerId" ON "sm_Construction" ("CustomerId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250312081709_add_table_sm_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "StatusCode" text NULL;

ALTER TABLE "sm_Construction" ADD "StatusName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250312085523_add_field_StatusCode_and_StatusName', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "DistrictCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Construction" ADD "DistrictName" text NULL;

ALTER TABLE "sm_Construction" ADD "ProvinceCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Construction" ADD "ProvinceName" text NULL;

ALTER TABLE "sm_Construction" ADD "WardCode" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Construction" ADD "WardName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250312092050_ref_table_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "ConstructionId" uuid NULL;

ALTER TABLE "sm_PurchaseOrder" ADD "ConstructionId" uuid NULL;

CREATE INDEX "IX_sm_SalesOrder_ConstructionId" ON "sm_SalesOrder" ("ConstructionId");

CREATE INDEX "IX_sm_PurchaseOrder_ConstructionId" ON "sm_PurchaseOrder" ("ConstructionId");

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "FK_sm_PurchaseOrder_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_SalesOrder" ADD CONSTRAINT "FK_sm_SalesOrder_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250312100810_add_fk_constructionId_into_purchaseOrder_and_salesOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Contract" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "DocumentTypeCode" text NULL,
    "DocumentTypeName" text NULL,
    "ParentId" uuid NULL,
    "ConstructionCategory" text NULL,
    "ConstructionId" uuid NOT NULL,
    "ValueAmount" numeric NOT NULL,
    "VatPercent" numeric NOT NULL,
    "TotalAmount" numeric NOT NULL,
    "StartDate" timestamp without time zone NOT NULL,
    "EndDate" timestamp without time zone NOT NULL,
    "StatusCode" text NULL,
    "StatusName" text NULL,
    "StatusColor" text NULL,
    "Note" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NOT NULL,
    "LastModifiedOnDate" timestamp without time zone NOT NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "ApplicationId" uuid NOT NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Contract" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Contract_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Contract_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Construction_TenantId" ON "sm_Construction" ("TenantId");

CREATE INDEX "IX_sm_Contract_ConstructionId" ON "sm_Contract" ("ConstructionId");

CREATE INDEX "IX_sm_Contract_TenantId" ON "sm_Contract" ("TenantId");

ALTER TABLE "sm_Construction" ADD CONSTRAINT "FK_sm_Construction_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250313031749_Add-sm_Contract-entity-class-with-properties-and-annotations', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Contract" DROP COLUMN "ApplicationId";

ALTER TABLE "sm_Contract" ALTER COLUMN "LastModifiedOnDate" DROP NOT NULL;

ALTER TABLE "sm_Contract" ALTER COLUMN "LastModifiedByUserId" DROP NOT NULL;

ALTER TABLE "sm_Contract" ADD "CreatedByUserName" text NULL;

ALTER TABLE "sm_Contract" ADD "DocumentTypeColor" text NULL;

ALTER TABLE "sm_Contract" ADD "LastModifiedByUserName" text NULL;

CREATE INDEX "IX_sm_Contract_ParentId" ON "sm_Contract" ("ParentId");

ALTER TABLE "sm_Contract" ADD CONSTRAINT "FK_sm_Contract_sm_Contract_ParentId" FOREIGN KEY ("ParentId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250313064636_Refactor-sm_Contract-class-and-add-new-properties', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "ConstructionId" uuid NULL;

ALTER TABLE "sm_Cashbook_Transaction" ADD "ContractId" uuid NULL;

CREATE INDEX "IX_sm_Cashbook_Transaction_ConstructionId" ON "sm_Cashbook_Transaction" ("ConstructionId");

CREATE INDEX "IX_sm_Cashbook_Transaction_ContractId" ON "sm_Cashbook_Transaction" ("ContractId");

ALTER TABLE "sm_Cashbook_Transaction" ADD CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_Cashbook_Transaction" ADD CONSTRAINT "FK_sm_Cashbook_Transaction_sm_Contract_ContractId" FOREIGN KEY ("ContractId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250314014555_Add-CashbookTransaction-reference-to-sm_Contract', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "ListPredicateInventory" jsonb NULL;

ALTER TABLE "sm_Construction" ADD "ListTeamInventory" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250317045148_add_field_ListPredicateInventory_and_ListTeamInventory_into_sm_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Product" ADD "ActualQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Product" ADD "BalanceQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Product" ADD "PlanQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250317064136_ref_table_Product', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_MaterialRequest" (
  "Id" uuid NOT NULL,
  "Code" text NULL,
  "Content" text NULL,
  "DateProcess" timestamp without time zone NOT NULL,
  "Priority" text NULL,
  "PriorityName" text NULL,
  "StatusCode" text NULL,
  "StatusName" text NULL,
  "MaterialRequestItem" jsonb NULL,
  "ListHistoryProcess" jsonb NULL,
  "ConstructionId" uuid NOT NULL,
  "CreatedByUserId" uuid NOT NULL,
  "LastModifiedByUserId" uuid NULL,
  "LastModifiedOnDate" timestamp without time zone NULL,
  "CreatedOnDate" timestamp without time zone NOT NULL,
  "CreatedByUserName" text NULL,
  "LastModifiedByUserName" text NULL,
  "TenantId" uuid NULL,
  CONSTRAINT "PK_sm_MaterialRequest" PRIMARY KEY ("Id"),
  CONSTRAINT "FK_sm_MaterialRequest_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
  CONSTRAINT "FK_sm_MaterialRequest_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_MaterialRequest_ConstructionId" ON "sm_MaterialRequest" ("ConstructionId");

CREATE INDEX "IX_sm_MaterialRequest_TenantId" ON "sm_MaterialRequest" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250318033442_add_table_sm_MaterialRequest', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequest" ADD "Note" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250318035500_add_field_note_into_sm_MaterialRequest', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "Idm_Tenants" ADD "Attachments" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250318034516_add-attachment-tbl-Tenant', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "Idm_Tenants" ADD "PhoneNumber" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250318072059_add-phoneNumber-tbl-tenant', '6.0.27');

COMMIT;
START TRANSACTION;

ALTER TABLE "Idm_Tenants" DROP COLUMN "PhoneNụmber";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250318074235_remove-field-phoneNumber-bug-tenant', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_MaterialRequestItem" (
   "Id" uuid NOT NULL,
   "ProductId" uuid NOT NULL,
   "MaterialRequestId" uuid NOT NULL,
   "RequestQuantity" numeric NOT NULL,
   CONSTRAINT "PK_sm_MaterialRequestItem" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_MaterialRequestItem_sm_MaterialRequest_MaterialRequestId" FOREIGN KEY ("MaterialRequestId") REFERENCES "sm_MaterialRequest" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_MaterialRequestItem_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_MaterialRequestItem_MaterialRequestId" ON "sm_MaterialRequestItem" ("MaterialRequestId");

CREATE INDEX "IX_sm_MaterialRequestItem_ProductId" ON "sm_MaterialRequestItem" ("ProductId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320040425_add_table_sm_MaterialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "CreatedByUserId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "sm_MaterialRequestItem" ADD "CreatedByUserName" text NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "CreatedOnDate" timestamp without time zone NOT NULL DEFAULT TIMESTAMP '-infinity';

ALTER TABLE "sm_MaterialRequestItem" ADD "LastModifiedByUserId" uuid NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "LastModifiedByUserName" text NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "LastModifiedOnDate" timestamp without time zone NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "TenantId" uuid NULL;

CREATE INDEX "IX_sm_MaterialRequestItem_TenantId" ON "sm_MaterialRequestItem" ("TenantId");

ALTER TABLE "sm_MaterialRequestItem" ADD CONSTRAINT "FK_sm_MaterialRequestItem_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320041631_ref_table_sm_MaterialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SalesOrder" ADD "ContractId" uuid NULL;

ALTER TABLE "sm_PurchaseOrder" ADD "ContractId" uuid NULL;

CREATE INDEX "IX_sm_SalesOrder_ContractId" ON "sm_SalesOrder" ("ContractId");

CREATE INDEX "IX_sm_PurchaseOrder_ContractId" ON "sm_PurchaseOrder" ("ContractId");

ALTER TABLE "sm_PurchaseOrder" ADD CONSTRAINT "FK_sm_PurchaseOrder_sm_Contract_ContractId" FOREIGN KEY ("ContractId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_SalesOrder" ADD CONSTRAINT "FK_sm_SalesOrder_sm_Contract_ContractId" FOREIGN KEY ("ContractId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320075019_add_field_contractId_into_salesOrder_and_purchaseOrder', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_InventoryNote" ADD "ConstructionId" uuid NULL;

ALTER TABLE "sm_InventoryNote" ADD "ContractId" uuid NULL;

CREATE INDEX "IX_sm_InventoryNote_ConstructionId" ON "sm_InventoryNote" ("ConstructionId");

CREATE INDEX "IX_sm_InventoryNote_ContractId" ON "sm_InventoryNote" ("ContractId");

ALTER TABLE "sm_InventoryNote" ADD CONSTRAINT "FK_sm_InventoryNote_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_InventoryNote" ADD CONSTRAINT "FK_sm_InventoryNote_sm_Contract_ContractId" FOREIGN KEY ("ContractId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320070920_add-fk-contract-and-construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_ConstructionItems" (
   "Id" uuid NOT NULL,
   "ProductId" uuid NOT NULL,
   "ConstructionId" uuid NOT NULL,
   "PlannedQuantity" text NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_ConstructionItems" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_ConstructionItems_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ConstructionItems_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ConstructionItems_sm_Product_ProductId" FOREIGN KEY ("ProductId") REFERENCES "sm_Product" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_ConstructionItems_ConstructionId" ON "sm_ConstructionItems" ("ConstructionId");

CREATE INDEX "IX_sm_ConstructionItems_ProductId" ON "sm_ConstructionItems" ("ProductId");

CREATE INDEX "IX_sm_ConstructionItems_TenantId" ON "sm_ConstructionItems" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320094719_add_table_sm_ConstructionItems', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "Code" text NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "Name" text NULL;

ALTER TABLE "sm_MaterialRequestItem" ADD "Unit" text NULL;

ALTER TABLE "sm_ConstructionItems" ADD "Code" text NULL;

ALTER TABLE "sm_ConstructionItems" ADD "Name" text NULL;

ALTER TABLE "sm_ConstructionItems" ADD "Unit" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320101108_add_field_code_name_unit_into_sm_ConstructionItems_and_sm_MaterialRequestItems', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "PlannedQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_ConstructionItems" ALTER COLUMN "PlannedQuantity" TYPE numeric;

ALTER TABLE "sm_ConstructionItems" ALTER COLUMN "PlannedQuantity" SET NOT NULL;

ALTER TABLE "sm_ConstructionItems" ALTER COLUMN "PlannedQuantity" SET DEFAULT 0.0;

ALTER TABLE "sm_ConstructionItems" ADD "GapQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_ConstructionItems" ADD "RealQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320105448_ref_table_ConstructionItem_and_MaterialConstructionItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionItems" DROP COLUMN "PlannedQuantity";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320110432_remove_field_planQuantity_in_sm_ConstructionItems', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionItems" ADD "PlannedQuantity" numeric NOT NULL DEFAULT 0.0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320111218_add_field_PlannedQuantity_into_constructionItems_and_sm_materialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "LineNo" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_ConstructionItems" ADD "LineNo" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250321010846_add_field_LineNo_into_constuctionItems_and_materialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "LineNote" text NULL;

ALTER TABLE "sm_ConstructionItems" ADD "LineNote" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250321070132_add_field_LineNote_into_ConstructionItems_and_MaterialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_AdvanceRequest" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Content" text NULL,
    "ConstructionId" uuid NOT NULL,
    "PriorityLevelCode" text NULL,
    "PriorityLevelName" text NULL,
    "PriorityLevelColor" text NULL,
    "DueDate" timestamp without time zone NOT NULL,
    "Note" text NULL,
    "StatusCode" text NULL,
    "StatusName" text NULL,
    "StatusColor" text NULL,
    "TotalLineAmount" numeric NOT NULL,
    "VatPercent" numeric NOT NULL,
    "TotalAmount" numeric NOT NULL,
    "AdvanceRequestHistories" jsonb NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_AdvanceRequest" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_AdvanceRequest_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_AdvanceRequest_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_AdvanceRequestItems" (
    "Id" uuid NOT NULL,
    "LineNumber" integer NOT NULL,
    "AdvancePurpose" text NULL,
    "Unit" text NULL,
    "Quantity" numeric NOT NULL,
    "UnitPrice" numeric NOT NULL,
    "LineAmount" numeric NOT NULL,
    "Note" text NULL,
    "sm_AdvanceRequestId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_AdvanceRequestItems" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_AdvanceRequestItems_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_AdvanceRequestItems_sm_AdvanceRequest_sm_AdvanceRequestId" FOREIGN KEY ("sm_AdvanceRequestId") REFERENCES "sm_AdvanceRequest" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_AdvanceRequest_ConstructionId" ON "sm_AdvanceRequest" ("ConstructionId");

CREATE INDEX "IX_sm_AdvanceRequest_TenantId" ON "sm_AdvanceRequest" ("TenantId");

CREATE INDEX "IX_sm_AdvanceRequestItems_sm_AdvanceRequestId" ON "sm_AdvanceRequestItems" ("sm_AdvanceRequestId");

CREATE INDEX "IX_sm_AdvanceRequestItems_TenantId" ON "sm_AdvanceRequestItems" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250322033532_add-advance-request-and-items-entities', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "ConstructionId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_MaterialRequestItem_ConstructionId" ON "sm_MaterialRequestItem" ("ConstructionId");

ALTER TABLE "sm_MaterialRequestItem" ADD CONSTRAINT "FK_sm_MaterialRequestItem_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250326022351_add_field_fk_constructionId_into_MaterialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Cashbook_Transaction" ADD "AdvanceRequestId" uuid NULL;

CREATE INDEX "IX_sm_Cashbook_Transaction_AdvanceRequestId" ON "sm_Cashbook_Transaction" ("AdvanceRequestId");

ALTER TABLE "sm_Cashbook_Transaction" ADD CONSTRAINT "FK_sm_Cashbook_Transaction_sm_AdvanceRequest_AdvanceRequestId" FOREIGN KEY ("AdvanceRequestId") REFERENCES "sm_AdvanceRequest" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250326030037_add-fk-advance-request', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_MaterialRequestItem" ADD "IsApprove" boolean NOT NULL DEFAULT FALSE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250326064022_add_field_isApprove_into_sm_MaterialRequestItem', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Return_Order" ADD "ConstructionId" uuid NULL;

ALTER TABLE "sm_Return_Order" ADD "ContractId" uuid NULL;

CREATE INDEX "IX_sm_Return_Order_ConstructionId" ON "sm_Return_Order" ("ConstructionId");

CREATE INDEX "IX_sm_Return_Order_ContractId" ON "sm_Return_Order" ("ContractId");

ALTER TABLE "sm_Return_Order" ADD CONSTRAINT "FK_sm_Return_Order_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_Return_Order" ADD CONSTRAINT "FK_sm_Return_Order_sm_Contract_ContractId" FOREIGN KEY ("ContractId") REFERENCES "sm_Contract" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250327031038_add_field_constructionId_and_contractId_into_sm_OrderReturn', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionItems" ADD "PlannedDefaultQuantity" numeric NOT NULL DEFAULT 0.0;

ALTER TABLE "sm_Construction" ADD "ConstructionAttachments" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250327063752_add_field_defaultPlannedQuantity_into_sm_Constructionitem_and_ConstructionAttachmenr_into_sm_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_ConstructionActivityLog" (
   "Id" uuid NOT NULL,
   "UserName" text NULL,
   "Description" text NULL,
   "CodeLinkDescription" text NULL,
   "OrderId" uuid NOT NULL,
   "ConstructionId" uuid NOT NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_ConstructionActivityLog" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_ConstructionActivityLog_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ConstructionActivityLog_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_ConstructionActivityLog_ConstructionId" ON "sm_ConstructionActivityLog" ("ConstructionId");

CREATE INDEX "IX_sm_ConstructionActivityLog_TenantId" ON "sm_ConstructionActivityLog" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250331032553_add_table_sm_ConstructionActivitylog', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionActivityLog" ADD "AvatarUrl" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250331042356_add_field_avatarUrl_into_sm_ConstructionActivityLog', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionActivityLog" ALTER COLUMN "ConstructionId" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250401020637_change_type_constructionId_in_sm_ConstructionActivityLog', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE TABLE "sm_TaskManagement" (
    "Id" uuid NOT NULL,
    "ParentId" uuid NULL,
    "Title" text NULL,
    "Type" text NULL,
    "Description" text NULL,
    "ProjectId" uuid NULL,
    "Status" text NULL,
    "Attachments" jsonb NULL,
    "DueDate" timestamp without time zone NOT NULL,
    "StartDate" timestamp without time zone NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskManagement" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskManagement_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagement_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagement_sm_TaskManagement_ParentId" FOREIGN KEY ("ParentId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskManagementAssignee" (
    "Id" uuid NOT NULL,
    "TaskManagementId" uuid NOT NULL,
    "UserId" uuid NOT NULL,
    "sm_TaskManagementId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskManagementAssignee" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskManagementAssignee_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagementAssignee_sm_TaskManagement_sm_TaskManageme~" FOREIGN KEY ("sm_TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskManagementComment" (
    "Id" uuid NOT NULL,
    "TaskManagementId" uuid NOT NULL,
    "Content" text NULL,
    "sm_TaskManagementId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskManagementComment" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskManagementComment_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagementComment_sm_TaskManagement_sm_TaskManagemen~" FOREIGN KEY ("sm_TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskManagementHistory" (
    "Id" uuid NOT NULL,
    "TaskManagementId" uuid NOT NULL,
    "Action" text NULL,
    "sm_TaskManagementId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskManagementHistory" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskManagementHistory_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagementHistory_sm_TaskManagement_sm_TaskManagemen~" FOREIGN KEY ("sm_TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_TaskManagement_ParentId" ON "sm_TaskManagement" ("ParentId");

CREATE INDEX "IX_sm_TaskManagement_ProjectId" ON "sm_TaskManagement" ("ProjectId");

CREATE INDEX "IX_sm_TaskManagement_TenantId" ON "sm_TaskManagement" ("TenantId");

CREATE INDEX "IX_sm_TaskManagementAssignee_sm_TaskManagementId" ON "sm_TaskManagementAssignee" ("sm_TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementAssignee_TenantId" ON "sm_TaskManagementAssignee" ("TenantId");

CREATE INDEX "IX_sm_TaskManagementComment_sm_TaskManagementId" ON "sm_TaskManagementComment" ("sm_TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementComment_TenantId" ON "sm_TaskManagementComment" ("TenantId");

CREATE INDEX "IX_sm_TaskManagementHistory_sm_TaskManagementId" ON "sm_TaskManagementHistory" ("sm_TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementHistory_TenantId" ON "sm_TaskManagementHistory" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250320075411_add-table-task-management', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_TaskManagementComment" ADD "TaskManagementCommentReplyId" uuid NULL;

CREATE TABLE "sm_TaskManagementMileStone" (
    "Id" uuid NOT NULL,
    "TaskManagementId" uuid NOT NULL,
    "Description" text NULL,
    "DueDate" timestamp without time zone NOT NULL,
    "StartDate" timestamp without time zone NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskManagementMileStone" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskManagementMileStone_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskManagementMileStone_sm_TaskManagement_TaskManagement~" FOREIGN KEY ("TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_TaskManagementMileStone_TaskManagementId" ON "sm_TaskManagementMileStone" ("TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementMileStone_TenantId" ON "sm_TaskManagementMileStone" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250324064259_add-task-management-milestone', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_TaskManagementAssignee" DROP CONSTRAINT "FK_sm_TaskManagementAssignee_sm_TaskManagement_sm_TaskManageme~";

ALTER TABLE "sm_TaskManagementComment" DROP CONSTRAINT "FK_sm_TaskManagementComment_sm_TaskManagement_sm_TaskManagemen~";

ALTER TABLE "sm_TaskManagementHistory" DROP CONSTRAINT "FK_sm_TaskManagementHistory_sm_TaskManagement_sm_TaskManagemen~";

DROP INDEX "IX_sm_TaskManagementHistory_sm_TaskManagementId";

DROP INDEX "IX_sm_TaskManagementComment_sm_TaskManagementId";

DROP INDEX "IX_sm_TaskManagementAssignee_sm_TaskManagementId";

ALTER TABLE "sm_TaskManagementHistory" DROP COLUMN "sm_TaskManagementId";

ALTER TABLE "sm_TaskManagementComment" DROP COLUMN "sm_TaskManagementId";

ALTER TABLE "sm_TaskManagementAssignee" DROP COLUMN "sm_TaskManagementId";

CREATE INDEX "IX_sm_TaskManagementHistory_TaskManagementId" ON "sm_TaskManagementHistory" ("TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementComment_TaskManagementId" ON "sm_TaskManagementComment" ("TaskManagementId");

CREATE INDEX "IX_sm_TaskManagementAssignee_TaskManagementId" ON "sm_TaskManagementAssignee" ("TaskManagementId");

ALTER TABLE "sm_TaskManagementAssignee" ADD CONSTRAINT "FK_sm_TaskManagementAssignee_sm_TaskManagement_TaskManagementId" FOREIGN KEY ("TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_TaskManagementComment" ADD CONSTRAINT "FK_sm_TaskManagementComment_sm_TaskManagement_TaskManagementId" FOREIGN KEY ("TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_TaskManagementHistory" ADD CONSTRAINT "FK_sm_TaskManagementHistory_sm_TaskManagement_TaskManagementId" FOREIGN KEY ("TaskManagementId") REFERENCES "sm_TaskManagement" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250324065554_add-fk-task-management-related-table', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_TaskManagementMileStone" ADD "Title" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250325075954_add-title-table-milestone', '6.0.27');

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_TaskManagement" DROP CONSTRAINT "FK_sm_TaskManagement_mk_DuAn_ProjectId";

ALTER TABLE "sm_TaskManagement" RENAME COLUMN "ProjectId" TO "ConstructionId";

ALTER INDEX "IX_sm_TaskManagement_ProjectId" RENAME TO "IX_sm_TaskManagement_ConstructionId";

ALTER TABLE "sm_TaskManagement" ADD CONSTRAINT "FK_sm_TaskManagement_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250325081751_ref-table-task-management', '6.0.27');

COMMIT;

START TRANSACTION;

CREATE INDEX "IX_sm_TaskManagementAssignee_UserId" ON "sm_TaskManagementAssignee" ("UserId");

ALTER TABLE "sm_TaskManagementAssignee" ADD CONSTRAINT "FK_sm_TaskManagementAssignee_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250326083447_mapping-username-in-taskmanagementassignee', '6.0.27');

COMMIT;


START TRANSACTION;

CREATE TABLE "sm_Feedback" (
    "Id" uuid NOT NULL,
    "Name" text NULL,
    "PhoneNumber" text NULL,
    "Module" text[] NULL,
    "Content" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Feedback" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Feedback_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Feedback_TenantId" ON "sm_Feedback" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250401091840_create-tbl-smFeedback', '6.0.27');

COMMIT;

--- Add column Rate to table sm_Feedback
START TRANSACTION;

ALTER TABLE "sm_Feedback" ADD "Rate" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250403040206_add-column-rate-to-tbl-smFeedback', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CodeType" ADD "IconClass" text NULL;

CREATE TABLE "sm_CodeType_Item" (
    "Id" uuid NOT NULL,
    "Code" text NULL,
    "Title" text NULL,
    "IconClass" text NULL,
    "CodeTypeId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_CodeType_Item" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_CodeType_Item_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_CodeType_Item_sm_CodeType_CodeTypeId" FOREIGN KEY ("CodeTypeId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_CodeType_Item_CodeTypeId" ON "sm_CodeType_Item" ("CodeTypeId");

CREATE INDEX "IX_sm_CodeType_Item_TenantId" ON "sm_CodeType_Item" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250403073210_add-IconClass-and-new-CodeType_Item-class', '6.0.27');

COMMIT;


---
---
---

COMMIT;

START TRANSACTION;

ALTER TABLE "sm_AssetAllocation" ADD "Code" text NULL;

ALTER TABLE "sm_AssetAllocation" ADD "RejectionReason" character varying(255) NULL;

ALTER TABLE "sm_AssetAllocation" ADD "Status" integer NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250403094447_add-status-code-RejectionReason-asset-allocation', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_CodeType_Item" ADD "LineNumber" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250404065711_add-LineNumber-property', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskManagement" DROP CONSTRAINT "FK_sm_TaskManagement_sm_TaskManagement_ParentId";

DROP INDEX "IX_sm_TaskManagement_ParentId";

ALTER TABLE "sm_TaskManagement" DROP COLUMN "ParentId";

CREATE INDEX "IX_sm_TaskManagementComment_CreatedByUserId" ON "sm_TaskManagementComment" ("CreatedByUserId");

ALTER TABLE "sm_TaskManagementComment" ADD CONSTRAINT "FK_sm_TaskManagementComment_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250423033321_add_field_fk_CreatedByUserId', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Kho" (
    "Id" uuid NOT NULL,
    "Ma" text NULL,
    "Ten" text NULL,
    "DiaChi" text NULL,
    "LoaiKho" text NULL,
    "IsCuaHang" boolean NOT NULL,
    "GhiChu" text NULL,
    "ProvinceCode" integer NULL,
    "ProvinceName" text NULL,
    "DistrictCode" integer NULL,
    "DistrictName" text NULL,
    "CommuneCode" integer NULL,
    "CommuneName" text NULL,
    "Latitude" double precision NULL,
    "Longitude" double precision NULL,
    "CustomerId" uuid NULL,
    "Binh" numeric NULL,
    "VoBinh" numeric NULL,
    "GasDu" numeric NULL,
    "Order" integer NULL,
    "IsInitialized" boolean NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Kho" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Kho_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Kho_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Kho_sm_Customer_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES "sm_Customer" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_PhuongTien" (
    "Id" uuid NOT NULL,
    "BienSoXe" text NULL,
    "SoKhung" text NULL,
    "SoMay" text NULL,
    "HangSanXuat" text NULL,
    "Model" text NULL,
    "NamSanXuat" text NULL,
    "TaiTrong" text NULL,
    "Active" boolean NOT NULL,
    "WarehouseId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_PhuongTien" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_PhuongTien_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_PhuongTien_sm_Kho_WarehouseId" FOREIGN KEY ("WarehouseId") REFERENCES "sm_Kho" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_LaiXe" (
    "Id" uuid NOT NULL,
    "IdPhuongTien" uuid NULL,
    "NgaySinh" timestamp without time zone NULL,
    "TenTaiXe" text NULL,
    "MaTaiXe" text NULL,
    "Cccd" text NULL,
    "Gplx" text NULL,
    "Active" boolean NOT NULL,
    "UserId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_LaiXe" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_LaiXe_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_LaiXe_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_LaiXe_sm_PhuongTien_IdPhuongTien" FOREIGN KEY ("IdPhuongTien") REFERENCES "sm_PhuongTien" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Kho_CreatedByUserId" ON "sm_Kho" ("CreatedByUserId");

CREATE INDEX "IX_sm_Kho_CustomerId" ON "sm_Kho" ("CustomerId");

CREATE INDEX "IX_sm_Kho_TenantId" ON "sm_Kho" ("TenantId");

CREATE UNIQUE INDEX "IX_sm_LaiXe_IdPhuongTien" ON "sm_LaiXe" ("IdPhuongTien");

CREATE INDEX "IX_sm_LaiXe_TenantId" ON "sm_LaiXe" ("TenantId");

CREATE INDEX "IX_sm_LaiXe_UserId" ON "sm_LaiXe" ("UserId");

CREATE INDEX "IX_sm_PhuongTien_TenantId" ON "sm_PhuongTien" ("TenantId");

CREATE UNIQUE INDEX "IX_sm_PhuongTien_WarehouseId" ON "sm_PhuongTien" ("WarehouseId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250425020109_add-sm_Kho-sm_LaiXe-and-sm_PhuongTien-classes', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_PhuongTien" ADD "LoaiXeId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE TABLE "sm_LoaiXe" (
    "Id" uuid NOT NULL,
    "TenLoaiXe" text NULL,
    "MoTa" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_LoaiXe" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_LoaiXe_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_PhuongTien_LoaiXeId" ON "sm_PhuongTien" ("LoaiXeId");

CREATE INDEX "IX_sm_LoaiXe_TenantId" ON "sm_LoaiXe" ("TenantId");

ALTER TABLE "sm_PhuongTien" ADD CONSTRAINT "FK_sm_PhuongTien_sm_LoaiXe_LoaiXeId" FOREIGN KEY ("LoaiXeId") REFERENCES "sm_LoaiXe" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250428070742_add_sm_LoaiXe', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_SocialMediaPost" (
   "Id" uuid NOT NULL,
   "ContentPostTitle" text NULL,
   "ContentPostBody" text NULL,
   "PostStatusCode" text NULL,
   "TagUserIds" text[] NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NOT NULL,
   "LastModifiedOnDate" timestamp without time zone NOT NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "ApplicationId" uuid NOT NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_SocialMediaPost" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_SocialMediaPost_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_SocialMediaPost_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_SocialMediaPost_CreatedByUserId" ON "sm_SocialMediaPost" ("CreatedByUserId");

CREATE INDEX "IX_sm_SocialMediaPost_TenantId" ON "sm_SocialMediaPost" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250429065626_add_table_sm_SocialMediaPost', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Comments" (
    "Id" uuid NOT NULL,
    "PostId" uuid NOT NULL,
    "Content" text NULL,
    "TagUserIdsInComment" text[] NULL,
    "CommentAttachments" jsonb NULL,
    "PostStatusCodeInComment" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Comments" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Comments_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Comments_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_Comments_sm_SocialMediaPost_PostId" FOREIGN KEY ("PostId") REFERENCES "sm_SocialMediaPost" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Comments_CreatedByUserId" ON "sm_Comments" ("CreatedByUserId");

CREATE INDEX "IX_sm_Comments_PostId" ON "sm_Comments" ("PostId");

CREATE INDEX "IX_sm_Comments_TenantId" ON "sm_Comments" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250429072941_add_table_sm_Comments', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_CommentItems" (
   "Id" uuid NOT NULL,
   "CommentId" uuid NOT NULL,
   "ContentItems" text NULL,
   "CommentItemsAttachments" jsonb NULL,
   "PostStatusCodeInCommentItems" text NULL,
   "TagUserIdsInCommentItems" text[] NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_CommentItems" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_CommentItems_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_CommentItems_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_CommentItems_sm_Comments_CommentId" FOREIGN KEY ("CommentId") REFERENCES "sm_Comments" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_CommentItems_CommentId" ON "sm_CommentItems" ("CommentId");

CREATE INDEX "IX_sm_CommentItems_CreatedByUserId" ON "sm_CommentItems" ("CreatedByUserId");

CREATE INDEX "IX_sm_CommentItems_TenantId" ON "sm_CommentItems" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250429074610_add_table_sm_CommentItems', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SocialMediaPost" DROP COLUMN "ApplicationId";

ALTER TABLE "sm_SocialMediaPost" ALTER COLUMN "LastModifiedOnDate" DROP NOT NULL;

ALTER TABLE "sm_SocialMediaPost" ALTER COLUMN "LastModifiedByUserId" DROP NOT NULL;

ALTER TABLE "sm_SocialMediaPost" ADD "CreatedByUserName" text NULL;

ALTER TABLE "sm_SocialMediaPost" ADD "LastModifiedByUserName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250429085736_add_field_createdByUserName', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SocialMediaPost" ADD "PostAttachments" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250506081809_add_field_PostAttachments_into_sm_SocialMediaPost', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_VehicleRequest" (
                                     "Id" uuid NOT NULL,
                                     "RequestCode" text NOT NULL,
                                     "UserId" uuid NOT NULL,
                                     "UserName" text NULL,
                                     "DepartmentId" uuid NOT NULL,
                                     "DepartmentName" text NULL,
                                     "ContactPhone" character varying(20) NULL,
                                     "ProjectId" uuid NULL,
                                     "ProjectName" text NULL,
                                     "Purpose" character varying(500) NOT NULL,
                                     "Priority" integer NOT NULL,
                                     "NumPassengers" integer NOT NULL,
                                     "StartDateTime" timestamp without time zone NOT NULL,
                                     "EndDateTime" timestamp without time zone NOT NULL,
                                     "DepartureLocation" character varying(255) NOT NULL,
                                     "DestinationLocation" character varying(255) NOT NULL,
                                     "RequestedVehicleTypeId" uuid NULL,
                                     "RequestedVehicleTypeName" text NULL,
                                     "RequestedVehicleId" uuid NULL,
                                     "RequestedVehiclePlateNumber" character varying(20) NULL,
                                     "Notes" character varying(500) NULL,
                                     "Status" integer NOT NULL,
                                     "RejectNotes" character varying(500) NULL,
                                     "CreatedByUserId" uuid NOT NULL,
                                     "LastModifiedByUserId" uuid NULL,
                                     "LastModifiedOnDate" timestamp without time zone NULL,
                                     "CreatedOnDate" timestamp without time zone NOT NULL,
                                     "CreatedByUserName" text NULL,
                                     "LastModifiedByUserName" text NULL,
                                     "TenantId" uuid NULL,
                                     CONSTRAINT "PK_sm_VehicleRequest" PRIMARY KEY ("Id"),
                                     CONSTRAINT "FK_sm_VehicleRequest_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_idm_User_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_mk_DuAn_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "mk_DuAn" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_mk_PhongBan_DepartmentId" FOREIGN KEY ("DepartmentId") REFERENCES "mk_PhongBan" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_sm_LoaiXe_RequestedVehicleTypeId" FOREIGN KEY ("RequestedVehicleTypeId") REFERENCES "sm_LoaiXe" ("Id") ON DELETE CASCADE,
                                     CONSTRAINT "FK_sm_VehicleRequest_sm_PhuongTien_RequestedVehicleId" FOREIGN KEY ("RequestedVehicleId") REFERENCES "sm_PhuongTien" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_VehicleRequest_CreatedByUserId" ON "sm_VehicleRequest" ("CreatedByUserId");

CREATE INDEX "IX_sm_VehicleRequest_DepartmentId" ON "sm_VehicleRequest" ("DepartmentId");

CREATE INDEX "IX_sm_VehicleRequest_LastModifiedByUserId" ON "sm_VehicleRequest" ("LastModifiedByUserId");

CREATE INDEX "IX_sm_VehicleRequest_ProjectId" ON "sm_VehicleRequest" ("ProjectId");

CREATE INDEX "IX_sm_VehicleRequest_RequestedVehicleId" ON "sm_VehicleRequest" ("RequestedVehicleId");

CREATE INDEX "IX_sm_VehicleRequest_RequestedVehicleTypeId" ON "sm_VehicleRequest" ("RequestedVehicleTypeId");

CREATE INDEX "IX_sm_VehicleRequest_TenantId" ON "sm_VehicleRequest" ("TenantId");

CREATE INDEX "IX_sm_VehicleRequest_UserId" ON "sm_VehicleRequest" ("UserId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250511103907_add-vehicle-request', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_VehicleRequest" DROP CONSTRAINT "FK_sm_VehicleRequest_mk_DuAn_ProjectId";

ALTER TABLE "sm_VehicleRequest" ADD CONSTRAINT "FK_sm_VehicleRequest_sm_Construction_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250513072000_change-project-from-mk_DuAn-to-sm_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "idm_User" DROP CONSTRAINT "FK_idm_User_mk_PhongBan_IdPhongBan";

DROP INDEX "IX_idm_User_IdPhongBan";

ALTER TABLE "idm_User" DROP COLUMN "IdPhongBan";

ALTER TABLE "idm_User" ADD "MaPhongBan" text NULL;

ALTER TABLE "idm_User" ADD "MaTo" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250513073320_rename_field_IdPhongBan_and_remove_FK_IdPhongBan', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_VehicleRequest" DROP CONSTRAINT "FK_sm_VehicleRequest_mk_PhongBan_DepartmentId";

ALTER TABLE "sm_VehicleRequest" ADD CONSTRAINT "FK_sm_VehicleRequest_sm_CodeType_DepartmentId" FOREIGN KEY ("DepartmentId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250513100946_change-phongBan-from-mk_PhongBan-to-sm_CodeType', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" DROP CONSTRAINT "FK_sm_Construction_sm_Customer_CustomerId";

DROP INDEX "IX_sm_Construction_CustomerId";

ALTER TABLE "sm_Construction" DROP COLUMN "Address";

ALTER TABLE "sm_Construction" DROP COLUMN "ConstructionAttachments";

ALTER TABLE "sm_Construction" DROP COLUMN "CustomerId";

ALTER TABLE "sm_Construction" DROP COLUMN "DistrictCode";

ALTER TABLE "sm_Construction" DROP COLUMN "DistrictName";

ALTER TABLE "sm_Construction" DROP COLUMN "EndDate";

ALTER TABLE "sm_Construction" DROP COLUMN "ListPredicateInventory";

ALTER TABLE "sm_Construction" DROP COLUMN "ListTeamInventory";

ALTER TABLE "sm_Construction" DROP COLUMN "ProvinceCode";

ALTER TABLE "sm_Construction" DROP COLUMN "ProvinceName";

ALTER TABLE "sm_Construction" DROP COLUMN "StartDate";

ALTER TABLE "sm_Construction" DROP COLUMN "WardCode";

ALTER TABLE "sm_Construction" DROP COLUMN "WardName";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514023653_remove_unused_variable_in_sm_Constructions', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_ProjectTemplate" (
  "Id" uuid NOT NULL,
  "Code" text NOT NULL,
  "Name" text NOT NULL,
  "Description" text NULL,
  "CreatedByUserId" uuid NOT NULL,
  "LastModifiedByUserId" uuid NULL,
  "LastModifiedOnDate" timestamp without time zone NULL,
  "CreatedOnDate" timestamp without time zone NOT NULL,
  "CreatedByUserName" text NULL,
  "LastModifiedByUserName" text NULL,
  "TenantId" uuid NULL,
  CONSTRAINT "PK_sm_ProjectTemplate" PRIMARY KEY ("Id"),
  CONSTRAINT "FK_sm_ProjectTemplate_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TemplateStage" (
   "Id" uuid NOT NULL,
   "StepOrder" integer NOT NULL,
   "Name" text NOT NULL,
   "Description" text NULL,
   "ProjectTemplateId" uuid NOT NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_TemplateStage" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_TemplateStage_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_TemplateStage_sm_ProjectTemplate_ProjectTemplateId" FOREIGN KEY ("ProjectTemplateId") REFERENCES "sm_ProjectTemplate" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_ProjectTemplate_TenantId" ON "sm_ProjectTemplate" ("TenantId");

CREATE INDEX "IX_sm_TemplateStage_ProjectTemplateId" ON "sm_TemplateStage" ("ProjectTemplateId");

CREATE INDEX "IX_sm_TemplateStage_TenantId" ON "sm_TemplateStage" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514025621_add_ProjectTemplate_and_TemplateStage', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "CompletionByCompany" text NULL;

ALTER TABLE "sm_Construction" ADD "CompletionByInvestor" text NULL;

ALTER TABLE "sm_Construction" ADD "DeliveryDate" timestamp without time zone NULL;

ALTER TABLE "sm_Construction" ADD "DocumentStatusCode" text NULL;

ALTER TABLE "sm_Construction" ADD "DocumentStatusName" text NULL;

ALTER TABLE "sm_Construction" ADD "ExecutionStatusCode" text NULL;

ALTER TABLE "sm_Construction" ADD "ExecutionStatusName" text NULL;

ALTER TABLE "sm_Construction" ADD "InvestorCode" text NULL;

ALTER TABLE "sm_Construction" ADD "OwnerTypeCode" text NULL;

ALTER TABLE "sm_Construction" ADD "PriorityCode" text NULL;

ALTER TABLE "sm_Construction" ADD "PriorityName" text NULL;

ALTER TABLE "sm_Construction" ADD "VoltageTypeCode" text NULL;

CREATE TABLE "sm_ExecutionTeams" (
   "Id" uuid NOT NULL,
   "EmployeeId" uuid NOT NULL,
   "ConstructionId" uuid NOT NULL,
   "MaPhongBan" text NULL,
   "MaTo" text NULL,
   "EmployeeAvatarUrl" text NULL,
   "EmployeeName" text NULL,
   "UserType" text NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_ExecutionTeams" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_ExecutionTeams_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ExecutionTeams_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_ExecutionTeams_ConstructionId" ON "sm_ExecutionTeams" ("ConstructionId");

CREATE INDEX "IX_sm_ExecutionTeams_TenantId" ON "sm_ExecutionTeams" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514033013_refactor_table_sm_Constructions_and_add_sm_ExecutionTeams', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "ConstructionTemplateId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_Construction_ConstructionTemplateId" ON "sm_Construction" ("ConstructionTemplateId");

ALTER TABLE "sm_Construction" ADD CONSTRAINT "FK_sm_Construction_sm_ProjectTemplate_ConstructionTemplateId" FOREIGN KEY ("ConstructionTemplateId") REFERENCES "sm_ProjectTemplate" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514035822_add_field_constructionTemplateId_into_sm_Constructions', '6.0.27');

COMMIT;

-- Create table sm_IssueManagement--
START TRANSACTION;

CREATE TABLE "sm_IssueManagement" (
    "Id" uuid NOT NULL,
    "Code" character varying(64) NULL,
    "ExpiryDate" timestamp without time zone NULL,
    "PriorityLevel" text NULL,
    "Content" text NULL,
    "Description" character varying(512) NULL,
    "Status" character varying(64) NULL,
    "ConstructionId" uuid NULL,
    "IdChucVu" uuid NULL,
    "MaPhongBan" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_IssueManagement" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_IssueManagement_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_IssueManagement_mk_ChucVu_IdChucVu" FOREIGN KEY ("IdChucVu") REFERENCES "mk_ChucVu" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_IssueManagement_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_IssueManagement_ConstructionId" ON "sm_IssueManagement" ("ConstructionId");

CREATE INDEX "IX_sm_IssueManagement_IdChucVu" ON "sm_IssueManagement" ("IdChucVu");

CREATE INDEX "IX_sm_IssueManagement_TenantId" ON "sm_IssueManagement" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514071007_create-table-smIssueManagement', '6.0.27');

COMMIT;

--- Add field UserId ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "UserId" uuid NULL;

CREATE INDEX "IX_sm_IssueManagement_UserId" ON "sm_IssueManagement" ("UserId");

ALTER TABLE "sm_IssueManagement" ADD CONSTRAINT "FK_sm_IssueManagement_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514082658_add-field-UserId-to-tbl-smIssueManagement', '6.0.27');

COMMIT;

--- Remove unused fields ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" DROP CONSTRAINT "FK_sm_IssueManagement_mk_ChucVu_IdChucVu";

DROP INDEX "IX_sm_IssueManagement_IdChucVu";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "IdChucVu";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "MaPhongBan";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250514084610_remove-unused-fields', '6.0.27');

COMMIT;

---
---
---

--- Add table sm_Investor and sm_InvestorType ---
START TRANSACTION;

CREATE TABLE "sm_InvestorType" (
   "Id" uuid NOT NULL,
   "Code" text NULL,
   "Name" text NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_InvestorType" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_InvestorType_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_Investor" (
   "Id" uuid NOT NULL,
   "Code" text NULL,
   "Name" text NULL,
   "InvestorTypeId" uuid NOT NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_Investor" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_Investor_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_Investor_sm_InvestorType_InvestorTypeId" FOREIGN KEY ("InvestorTypeId") REFERENCES "sm_InvestorType" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Investor_InvestorTypeId" ON "sm_Investor" ("InvestorTypeId");

CREATE INDEX "IX_sm_Investor_TenantId" ON "sm_Investor" ("TenantId");

CREATE INDEX "IX_sm_InvestorType_TenantId" ON "sm_InvestorType" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250516034058_add_table_sm_Investor_and_sm_InvestorType', '6.0.27');

COMMIT;

---
---
---

--- Remove field InvestorCode and add field FK InvestorId into sm_Construction
START TRANSACTION;

ALTER TABLE "sm_Construction" DROP COLUMN "InvestorCode";

ALTER TABLE "sm_Construction" ADD "InvestorId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_Construction_InvestorId" ON "sm_Construction" ("InvestorId");

ALTER TABLE "sm_Construction" ADD CONSTRAINT "FK_sm_Construction_sm_Investor_InvestorId" FOREIGN KEY ("InvestorId") REFERENCES "sm_Investor" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250516075650_remove_field_investorCode_and_add_field_InvestorId_into_sm_Construction', '6.0.27');
    
COMMIT;

--- Add field reasonCancel to table sm_IssueManagement ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "ReasonCancel" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250516082616_add-field-reasonCancel-to-tbl-smIssueManagement', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Contract" DROP CONSTRAINT "FK_sm_Contract_sm_Contract_ParentId";

DROP INDEX "IX_sm_Contract_ParentId";

ALTER TABLE "sm_Contract" DROP COLUMN "ConstructionCategory";

ALTER TABLE "sm_Contract" DROP COLUMN "DocumentTypeCode";

ALTER TABLE "sm_Contract" DROP COLUMN "DocumentTypeColor";

ALTER TABLE "sm_Contract" DROP COLUMN "DocumentTypeName";

ALTER TABLE "sm_Contract" DROP COLUMN "EndDate";

ALTER TABLE "sm_Contract" DROP COLUMN "Note";

ALTER TABLE "sm_Contract" DROP COLUMN "ParentId";

ALTER TABLE "sm_Contract" DROP COLUMN "StartDate";

ALTER TABLE "sm_Contract" DROP COLUMN "StatusCode";

ALTER TABLE "sm_Contract" DROP COLUMN "StatusColor";

ALTER TABLE "sm_Contract" DROP COLUMN "StatusName";

ALTER TABLE "sm_Contract" DROP COLUMN "TotalAmount";

ALTER TABLE "sm_Contract" DROP COLUMN "ValueAmount";

ALTER TABLE "sm_Contract" DROP COLUMN "VatPercent";

ALTER TABLE "sm_Contract" ALTER COLUMN "Code" TYPE character varying(100);
UPDATE "sm_Contract" SET "Code" = '' WHERE "Code" IS NULL;
ALTER TABLE "sm_Contract" ALTER COLUMN "Code" SET NOT NULL;
ALTER TABLE "sm_Contract" ALTER COLUMN "Code" SET DEFAULT '';

ALTER TABLE "sm_Contract" ADD "AcceptanceValueBeforeVatAmount" numeric(18,2) NULL;

ALTER TABLE "sm_Contract" ADD "AcceptanceYear" integer NULL;

ALTER TABLE "sm_Contract" ADD "Appendices" jsonb NULL;

ALTER TABLE "sm_Contract" ADD "ApprovalDate" timestamp without time zone NULL;

ALTER TABLE "sm_Contract" ADD "AssignmentAYear" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Contract" ADD "ConsultingServiceId" uuid NOT NULL;

ALTER TABLE "sm_Contract" ADD "ExpectedAcceptanceMonth" integer NULL;

ALTER TABLE "sm_Contract" ADD "ExpectedApprovalMonth" integer NULL;

ALTER TABLE "sm_Contract" ADD "ExpectedVolume" numeric(18,2) NULL;

ALTER TABLE "sm_Contract" ADD "InvoiceIssuanceDate" timestamp without time zone NULL;

ALTER TABLE "sm_Contract" ADD "SettlementValueAmount" numeric(18,2) NULL;

ALTER TABLE "sm_Contract" ADD "Status" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Contract" ADD "TaxRatePercentage" real NULL;

ALTER TABLE "sm_Contract" ADD "TemplateStageId" uuid NOT NULL;

ALTER TABLE "sm_Contract" ADD "ValueBeforeVatAmount" numeric(18,2) NULL;

CREATE INDEX "IX_sm_Contract_ConsultingServiceId" ON "sm_Contract" ("ConsultingServiceId");

CREATE INDEX "IX_sm_Contract_CreatedByUserId" ON "sm_Contract" ("CreatedByUserId");

CREATE INDEX "IX_sm_Contract_LastModifiedByUserId" ON "sm_Contract" ("LastModifiedByUserId");

CREATE INDEX "IX_sm_Contract_TemplateStageId" ON "sm_Contract" ("TemplateStageId");

ALTER TABLE "sm_Contract" ADD CONSTRAINT "FK_sm_Contract_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_Contract" ADD CONSTRAINT "FK_sm_Contract_idm_User_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_Contract" ADD CONSTRAINT "FK_sm_Contract_sm_CodeType_ConsultingServiceId" FOREIGN KEY ("ConsultingServiceId") REFERENCES "sm_CodeType" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_Contract" ADD CONSTRAINT "FK_sm_Contract_sm_TemplateStage_TemplateStageId" FOREIGN KEY ("TemplateStageId") REFERENCES "sm_TemplateStage" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250517034535_remake-contract', '6.0.27');

COMMIT;

--- Create table IssueActivityLog ---

START TRANSACTION;

CREATE TABLE "sm_IssueActivityLog" (
    "Id" uuid NOT NULL,
    "UserName" text NULL,
    "AvatarUrl" text NULL,
    "Description" text NULL,
    "CodeLinkDescription" text NULL,
    "OrderId" uuid NOT NULL,
    "ConstructionId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_IssueActivityLog" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_IssueActivityLog_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_IssueActivityLog_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_IssueActivityLog_ConstructionId" ON "sm_IssueActivityLog" ("ConstructionId");

CREATE INDEX "IX_sm_IssueActivityLog_TenantId" ON "sm_IssueActivityLog" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250517043401_create-table-smIssueActivityLog', '6.0.27');

COMMIT;

--- Add field ActivityLog ---

START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "ActivityLogs" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250517080337_add-fields-activityLogs-to-tbl-smIssueManagement', '6.0.27');

COMMIT;

---
---
---

--- Create table sm_ConstructionWeekReport

START TRANSACTION;

CREATE TABLE "sm_ConstructionWeekReport" (
   "Id" uuid NOT NULL,
   "Title" text NULL,
   "LastWeekPlan" text NULL,
   "ProcessResult" text NULL,
   "NextWeekPlan" text NULL,
   "FileAttachments" jsonb NULL,
   "ConstructionId" uuid NOT NULL,
   "CreatedByUserId" uuid NOT NULL,
   "LastModifiedByUserId" uuid NULL,
   "LastModifiedOnDate" timestamp without time zone NULL,
   "CreatedOnDate" timestamp without time zone NOT NULL,
   "CreatedByUserName" text NULL,
   "LastModifiedByUserName" text NULL,
   "TenantId" uuid NULL,
   CONSTRAINT "PK_sm_ConstructionWeekReport" PRIMARY KEY ("Id"),
   CONSTRAINT "FK_sm_ConstructionWeekReport_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ConstructionWeekReport_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
   CONSTRAINT "FK_sm_ConstructionWeekReport_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_ConstructionWeekReport_ConstructionId" ON "sm_ConstructionWeekReport" ("ConstructionId");

CREATE INDEX "IX_sm_ConstructionWeekReport_CreatedByUserId" ON "sm_ConstructionWeekReport" ("CreatedByUserId");

CREATE INDEX "IX_sm_ConstructionWeekReport_TenantId" ON "sm_ConstructionWeekReport" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250519100533_add_table_sm_ConstructionWeekReport', '6.0.27');

COMMIT;

---
---
---

--- Add field StatusCode and StatusName into sm_ConstructionWeekReport ---

START TRANSACTION;

ALTER TABLE "sm_ConstructionWeekReport" ADD "StatusCode" text NULL;

ALTER TABLE "sm_ConstructionWeekReport" ADD "StatusName" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250520015230_add_field_statusCode_and_statusName_into_sm_ConstructionWeekReport', '6.0.27');

COMMIT;

---
---
---

--- Add field Code into sm_ConstructionWeekReport ---

START TRANSACTION;

ALTER TABLE "sm_ConstructionWeekReport" ADD "Code" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250520025733_add_field_code_into_sm_ConstructionWeekReport', '6.0.27');

COMMIT;

---
---
---

--- Remove unused field in sm_CustomerServiceComment ---
START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" DROP CONSTRAINT "FK_sm_CustomerServiceComment_sm_Customer_CustomerId";

ALTER TABLE "sm_CustomerServiceComment" DROP CONSTRAINT "FK_sm_CustomerServiceComment_sm_LichSuChamSoc_CustomerServiceId";

DROP INDEX "IX_sm_CustomerServiceComment_CustomerId";

DROP INDEX "IX_sm_CustomerServiceComment_CustomerServiceId";

ALTER TABLE "sm_CustomerServiceComment" DROP COLUMN "CustomerId";

ALTER TABLE "sm_CustomerServiceComment" DROP COLUMN "CustomerServiceId";

ALTER TABLE "sm_CustomerServiceComment" DROP COLUMN "CustomerType";

ALTER TABLE "sm_CustomerServiceComment" DROP COLUMN "IsSystemLog";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250520045930_remove_unused_field_sm_CustomerServiceComment', '6.0.27');

COMMIT;

---
---
---

--- Refactor table sm_CustomerServiceComment_and_sm_ConstructionWeekReport
START TRANSACTION;

ALTER TABLE "sm_CustomerServiceComment" ADD "ConstructionId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

ALTER TABLE "sm_CustomerServiceComment" ADD "TagIds" text[] NULL;

ALTER TABLE "sm_ConstructionWeekReport" ADD "EndDate" timestamp without time zone NULL;

ALTER TABLE "sm_ConstructionWeekReport" ADD "StartDate" timestamp without time zone NULL;

CREATE INDEX "IX_sm_CustomerServiceComment_ConstructionId" ON "sm_CustomerServiceComment" ("ConstructionId");

ALTER TABLE "sm_CustomerServiceComment" ADD CONSTRAINT "FK_sm_CustomerServiceComment_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250520063530_add_field_startDate_and_endDate_in_sm_ConstructionWeekReport_and_refactor_table_sm_CustomerServiceComment', '6.0.27');

COMMIT;

--- Add column Attachment to table sm_IssueManagement ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "Attachments" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250521033502_add-field-attachments-to-table-smIssueManagement', '6.0.27');

COMMIT;

--- Add columns Attachment, content for resolve issue ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "AttachmentsResolve" jsonb NULL;

ALTER TABLE "sm_IssueManagement" ADD "ContentResolve" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250522090423_add-fields-for-resolve-issue', '6.0.27');

COMMIT;

--- Add columns reason for reopen issue ---

START TRANSACTION;

ALTER TABLE "sm_IssueManagement" ADD "ReasonReopen" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250522100425_add-field-reason-Reopen-for-resolve-issue', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Task" (
    "Id" uuid NOT NULL,
    "Code" text NOT NULL,
    "Name" text NOT NULL,
    "StartDateTime" timestamp without time zone NOT NULL,
    "EndDateTime" timestamp without time zone NOT NULL,
    "Description" text NOT NULL,
    "PriorityLevel" integer NOT NULL,
    "TemplateStages" jsonb NULL,
    "Attachments" jsonb NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_Task" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_Task_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_SubTask" (
    "Id" uuid NOT NULL,
    "Name" text NOT NULL,
    "IsCompleted" boolean NOT NULL,
    "DueDate" timestamp without time zone NULL,
    "UserId" uuid NULL,
    "Attachments" jsonb NULL,
    "TaskId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_SubTask" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_SubTask_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SubTask_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SubTask_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskApprover" (
    "Id" uuid NOT NULL,
    "UserId" uuid NULL,
    "TaskId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskApprover" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskApprover_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskApprover_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskApprover_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskExecutor" (
    "Id" uuid NOT NULL,
    "UserId" uuid NULL,
    "TaskId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskExecutor" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskExecutor_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskExecutor_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskExecutor_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_TaskUsageHistory" (
    "Id" uuid NOT NULL,
    "TaskId" uuid NOT NULL,
    "ActivityType" integer NOT NULL,
    "ExecutionDate" timestamp without time zone NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskUsageHistory" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskUsageHistory_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskUsageHistory_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_SubTask_TaskId" ON "sm_SubTask" ("TaskId");

CREATE INDEX "IX_sm_SubTask_TenantId" ON "sm_SubTask" ("TenantId");

CREATE INDEX "IX_sm_SubTask_UserId" ON "sm_SubTask" ("UserId");

CREATE INDEX "IX_sm_Task_TenantId" ON "sm_Task" ("TenantId");

CREATE INDEX "IX_sm_TaskApprover_TaskId" ON "sm_TaskApprover" ("TaskId");

CREATE INDEX "IX_sm_TaskApprover_TenantId" ON "sm_TaskApprover" ("TenantId");

CREATE INDEX "IX_sm_TaskApprover_UserId" ON "sm_TaskApprover" ("UserId");

CREATE INDEX "IX_sm_TaskExecutor_TaskId" ON "sm_TaskExecutor" ("TaskId");

CREATE INDEX "IX_sm_TaskExecutor_TenantId" ON "sm_TaskExecutor" ("TenantId");

CREATE INDEX "IX_sm_TaskExecutor_UserId" ON "sm_TaskExecutor" ("UserId");

CREATE INDEX "IX_sm_TaskUsageHistory_TaskId" ON "sm_TaskUsageHistory" ("TaskId");

CREATE INDEX "IX_sm_TaskUsageHistory_TenantId" ON "sm_TaskUsageHistory" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250526063624_add-task-management', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Task" ADD "ConstructionId" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX "IX_sm_Task_ConstructionId" ON "sm_Task" ("ConstructionId");

ALTER TABLE "sm_Task" ADD CONSTRAINT "FK_sm_Task_sm_Construction_ConstructionId" FOREIGN KEY ("ConstructionId") REFERENCES "sm_Construction" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250526073806_add-fk-task-construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_SubTaskExecutor" (
    "Id" uuid NOT NULL,
    "UserId" uuid NULL,
    "SubTaskId" uuid NOT NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_SubTaskExecutor" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_SubTaskExecutor_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SubTaskExecutor_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_SubTaskExecutor_sm_SubTask_SubTaskId" FOREIGN KEY ("SubTaskId") REFERENCES "sm_SubTask" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_SubTaskExecutor_SubTaskId" ON "sm_SubTaskExecutor" ("SubTaskId");

CREATE INDEX "IX_sm_SubTaskExecutor_TenantId" ON "sm_SubTaskExecutor" ("TenantId");

CREATE INDEX "IX_sm_SubTaskExecutor_UserId" ON "sm_SubTaskExecutor" ("UserId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250526124447_add-sm_SubTaskExecutor', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250526125011_add-sm_SubTaskExecutor123', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_SubTask" DROP CONSTRAINT "FK_sm_SubTask_idm_User_UserId";

DROP INDEX "IX_sm_SubTask_UserId";

ALTER TABLE "sm_SubTask" DROP COLUMN "UserId";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527011251_remove-fk-user-subtask', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Task" DROP COLUMN "TemplateStages";

ALTER TABLE "sm_Task" ALTER COLUMN "StartDateTime" DROP NOT NULL;

ALTER TABLE "sm_Task" ALTER COLUMN "EndDateTime" DROP NOT NULL;

ALTER TABLE "sm_Task" ALTER COLUMN "Description" DROP NOT NULL;

ALTER TABLE "sm_Task" ALTER COLUMN "ConstructionId" DROP NOT NULL;

ALTER TABLE "sm_Task" ALTER COLUMN "Code" DROP NOT NULL;

ALTER TABLE "sm_Task" ADD "TemplateStageId" uuid NULL;

CREATE INDEX "IX_sm_Task_TemplateStageId" ON "sm_Task" ("TemplateStageId");

ALTER TABLE "sm_Task" ADD CONSTRAINT "FK_sm_Task_sm_TemplateStage_TemplateStageId" FOREIGN KEY ("TemplateStageId") REFERENCES "sm_TemplateStage" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527055131_update-properties-task-templateStage', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Construction" ADD "TemplateStages" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527073301_add_TemplateStages_Construction', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Task" ADD "Status" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527104905_add-status-task', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskUsageHistory" ALTER COLUMN "TaskId" DROP NOT NULL;

ALTER TABLE "sm_TaskUsageHistory" ADD "Description" text NULL;

CREATE INDEX "IX_sm_TaskUsageHistory_CreatedByUserId" ON "sm_TaskUsageHistory" ("CreatedByUserId");

ALTER TABLE "sm_TaskUsageHistory" ADD CONSTRAINT "FK_sm_TaskUsageHistory_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527112906_update-sm_TaskUsageHistory', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Task" ADD "StepOrder" integer NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250528032338_ add-steporder-task', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Contract" DROP COLUMN "SettlementValueAmount";

ALTER TABLE "sm_Contract" RENAME COLUMN "Status" TO "SupplementaryContractRequired";

ALTER TABLE "sm_Contract" RENAME COLUMN "InvoiceIssuanceDate" TO "SurveyAcceptanceRecordDate";

ALTER TABLE "sm_Contract" ADD "AcceptanceDocumentStatus" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Contract" ADD "AcceptancePlan" text NULL;

ALTER TABLE "sm_Contract" ADD "ContractDurationDays" integer NULL;

ALTER TABLE "sm_Contract" ADD "ContractNumber" character varying(100) NULL;

ALTER TABLE "sm_Contract" ADD "ContractSigningDate" timestamp without time zone NULL;

ALTER TABLE "sm_Contract" ADD "DesignApprovalDate" timestamp without time zone NULL;

ALTER TABLE "sm_Contract" ADD "HandoverRecordDate" timestamp without time zone NULL;

ALTER TABLE "sm_Contract" ADD "ImplementationStatus" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Contract" ADD "InvoiceIssuanceDates" jsonb NULL;

ALTER TABLE "sm_Contract" ADD "InvoiceStatus" integer NOT NULL DEFAULT 0;

ALTER TABLE "sm_Contract" ADD "Issues" text NULL;

ALTER TABLE "sm_Contract" ADD "Notes" text NULL;

ALTER TABLE "sm_Contract" ADD "SiteSurveyRecordDate" timestamp without time zone NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250528040111_add-more-fields-to-contract', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskUsageHistory" DROP COLUMN "ExecutionDate";

ALTER TABLE "sm_TaskUsageHistory" ADD "NameSubtask" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250529181430_add-NameSubTask-sm_TaskUsageHistory', '6.0.27');

COMMIT;

--- Add column to table sm_IssueActivityLog ---
START TRANSACTION;

ALTER TABLE "sm_IssueActivityLog" ADD "AttachmentsResolve" jsonb NULL;

ALTER TABLE "sm_IssueActivityLog" ADD "ContentResovle" text NULL;

ALTER TABLE "sm_IssueActivityLog" ADD "ReasonCancel" text NULL;

ALTER TABLE "sm_IssueActivityLog" ADD "ReasonReopen" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250530030504_add-fields-to-tbl-smIssueActivityLog', '6.0.27');

COMMIT;

---
---
---

--- retype ConstructionTemplateId can be null ---
START TRANSACTION;

ALTER TABLE "sm_Construction" ALTER COLUMN "ConstructionTemplateId" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250530030402_retype_ConstructionTemplateId_in_sm_Construction', '6.0.27');

COMMIT;
--- remove unused field(sm_IssueManagement) ---
START TRANSACTION;

ALTER TABLE "sm_IssueManagement" DROP COLUMN "ActivityLogs";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "AttachmentsResolve";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "ContentResolve";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "ReasonCancel";

ALTER TABLE "sm_IssueManagement" DROP COLUMN "ReasonReopen";

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250530034331_remove-unused-fields-tbl-smIssueManagement', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Task" ADD "IdTemplateStage" uuid NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250530072543_add-Id-TemplateStage-sm_Task', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_TaskComment" (
    "Id" uuid NOT NULL,
    "TaskId" uuid NOT NULL,
    "TagIds" text[] NULL,
    "Content" text NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskComment" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskComment_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskComment_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskComment_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_TaskComment_CreatedByUserId" ON "sm_TaskComment" ("CreatedByUserId");

CREATE INDEX "IX_sm_TaskComment_TaskId" ON "sm_TaskComment" ("TaskId");

CREATE INDEX "IX_sm_TaskComment_TenantId" ON "sm_TaskComment" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250531011154_add-TaskComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_TaskNotification" (
    "Id" uuid NOT NULL,
    "NotificationStatus" integer NOT NULL,
    "IsRead" boolean NOT NULL,
    "UserId" uuid NULL,
    "TaskId" uuid NULL,
    "CreatedByUserId" uuid NOT NULL,
    "LastModifiedByUserId" uuid NULL,
    "LastModifiedOnDate" timestamp without time zone NULL,
    "CreatedOnDate" timestamp without time zone NOT NULL,
    "CreatedByUserName" text NULL,
    "LastModifiedByUserName" text NULL,
    "TenantId" uuid NULL,
    CONSTRAINT "PK_sm_TaskNotification" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_sm_TaskNotification_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskNotification_idm_User_UserId" FOREIGN KEY ("UserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_sm_TaskNotification_sm_Task_TaskId" FOREIGN KEY ("TaskId") REFERENCES "sm_Task" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_TaskNotification_TaskId" ON "sm_TaskNotification" ("TaskId");

CREATE INDEX "IX_sm_TaskNotification_TenantId" ON "sm_TaskNotification" ("TenantId");

CREATE INDEX "IX_sm_TaskNotification_UserId" ON "sm_TaskNotification" ("UserId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250531093753_add-TaskNotication', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskNotification" ADD "ApproverId" uuid NULL;

ALTER TABLE "sm_TaskNotification" ADD "ExecutorId" uuid NULL;

CREATE INDEX "IX_sm_TaskNotification_ApproverId" ON "sm_TaskNotification" ("ApproverId");

CREATE INDEX "IX_sm_TaskNotification_ExecutorId" ON "sm_TaskNotification" ("ExecutorId");

ALTER TABLE "sm_TaskNotification" ADD CONSTRAINT "FK_sm_TaskNotification_sm_TaskApprover_ApproverId" FOREIGN KEY ("ApproverId") REFERENCES "sm_TaskApprover" ("Id") ON DELETE CASCADE;

ALTER TABLE "sm_TaskNotification" ADD CONSTRAINT "FK_sm_TaskNotification_sm_TaskExecutor_ExecutorId" FOREIGN KEY ("ExecutorId") REFERENCES "sm_TaskExecutor" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250531101051_update-TaskNotification', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskNotification" DROP CONSTRAINT "FK_sm_TaskNotification_sm_TaskApprover_ApproverId";

ALTER TABLE "sm_TaskNotification" DROP CONSTRAINT "FK_sm_TaskNotification_sm_TaskExecutor_ExecutorId";

DROP INDEX "IX_sm_TaskNotification_ApproverId";

DROP INDEX "IX_sm_TaskNotification_ExecutorId";

ALTER TABLE "sm_TaskNotification" DROP COLUMN "ApproverId";

ALTER TABLE "sm_TaskNotification" DROP COLUMN "ExecutorId";

ALTER TABLE "sm_TaskNotification" ADD "ApprovalType" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250531114321_add_ApprovalType_TaskNotification', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE INDEX "IX_sm_TaskNotification_CreatedByUserId" ON "sm_TaskNotification" ("CreatedByUserId");

ALTER TABLE "sm_TaskNotification" ADD CONSTRAINT "FK_sm_TaskNotification_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250601231424_update-sm_tasknotification', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_Contract" DROP CONSTRAINT "FK_sm_Contract_sm_TemplateStage_TemplateStageId";

DROP INDEX "IX_sm_Contract_TemplateStageId";

ALTER TABLE "sm_Contract" ADD "TemplateStage" jsonb NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250611031659_store-templateState-in-jsonb', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionActivityLog" ADD "ActionType" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250613015008_add_field_ActionType_into_ConstructionActivityLog', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_ConstructionActivityLog" ADD "StepOrder" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250614033900_add_field_stepOrder_into_sm_ConstructionActivityLog', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_Configuration" (
                                    "Key" char(20) NOT NULL,
                                    "Value" text NULL,
                                    "Description" text NULL,
                                    "CreatedByUserId" uuid NOT NULL,
                                    "LastModifiedByUserId" uuid NULL,
                                    "LastModifiedOnDate" timestamp without time zone NULL,
                                    "CreatedOnDate" timestamp without time zone NOT NULL,
                                    "CreatedByUserName" text NULL,
                                    "LastModifiedByUserName" text NULL,
                                    "TenantId" uuid NULL,
                                    CONSTRAINT "PK_sm_Configuration" PRIMARY KEY ("Key"),
                                    CONSTRAINT "FK_sm_Configuration_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_Configuration_TenantId" ON "sm_Configuration" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250615152234_add-configuration-table', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskNotification" ADD "AvatarUrl" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250616030055_add-AvatarUrl-TaskNotification', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

CREATE TABLE "sm_SocialPost" (
                                 "Id" uuid NOT NULL,
                                 "CreatedByUserId" uuid NOT NULL,
                                 "LastModifiedByUserId" uuid NULL,
                                 "LastModifiedOnDate" timestamp without time zone NULL,
                                 "CreatedOnDate" timestamp without time zone NOT NULL,
                                 "CreatedByUserName" text NULL,
                                 "LastModifiedByUserName" text NULL,
                                 "TenantId" uuid NULL,
                                 "Content" text NOT NULL,
                                 "Attachments" jsonb NULL,
                                 "Reactions" jsonb NULL,
                                 CONSTRAINT "PK_sm_SocialPost" PRIMARY KEY ("Id"),
                                 CONSTRAINT "FK_sm_SocialPost_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
                                 CONSTRAINT "FK_sm_SocialPost_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                 CONSTRAINT "FK_sm_SocialPost_idm_User_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE
);

CREATE TABLE "sm_SocialComment" (
                                    "Id" uuid NOT NULL,
                                    "PostId" uuid NOT NULL,
                                    "ParentCommentId" uuid NULL,
                                    "CreatedByUserId" uuid NOT NULL,
                                    "LastModifiedByUserId" uuid NULL,
                                    "LastModifiedOnDate" timestamp without time zone NULL,
                                    "CreatedOnDate" timestamp without time zone NOT NULL,
                                    "CreatedByUserName" text NULL,
                                    "LastModifiedByUserName" text NULL,
                                    "TenantId" uuid NULL,
                                    "Content" text NOT NULL,
                                    "Attachments" jsonb NULL,
                                    "Reactions" jsonb NULL,
                                    CONSTRAINT "PK_sm_SocialComment" PRIMARY KEY ("Id"),
                                    CONSTRAINT "FK_sm_SocialComment_Idm_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "Idm_Tenants" ("Id") ON DELETE CASCADE,
                                    CONSTRAINT "FK_sm_SocialComment_idm_User_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                    CONSTRAINT "FK_sm_SocialComment_idm_User_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES "idm_User" ("Id") ON DELETE CASCADE,
                                    CONSTRAINT "FK_sm_SocialComment_sm_SocialComment_ParentCommentId" FOREIGN KEY ("ParentCommentId") REFERENCES "sm_SocialComment" ("Id") ON DELETE CASCADE,
                                    CONSTRAINT "FK_sm_SocialComment_sm_SocialPost_PostId" FOREIGN KEY ("PostId") REFERENCES "sm_SocialPost" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_sm_SocialComment_CreatedByUserId" ON "sm_SocialComment" ("CreatedByUserId");

CREATE INDEX "IX_sm_SocialComment_LastModifiedByUserId" ON "sm_SocialComment" ("LastModifiedByUserId");

CREATE INDEX "IX_sm_SocialComment_ParentCommentId" ON "sm_SocialComment" ("ParentCommentId");

CREATE INDEX "IX_sm_SocialComment_PostId" ON "sm_SocialComment" ("PostId");

CREATE INDEX "IX_sm_SocialComment_TenantId" ON "sm_SocialComment" ("TenantId");

CREATE INDEX "IX_sm_SocialPost_CreatedByUserId" ON "sm_SocialPost" ("CreatedByUserId");

CREATE INDEX "IX_sm_SocialPost_LastModifiedByUserId" ON "sm_SocialPost" ("LastModifiedByUserId");

CREATE INDEX "IX_sm_SocialPost_TenantId" ON "sm_SocialPost" ("TenantId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250622184136_add-new-socialPost-and-socialComment', '6.0.27');

COMMIT;

---
---
---

START TRANSACTION;

ALTER TABLE "sm_TaskNotification" ADD "AdditionalData" jsonb NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250624174416_add-additionalData-to-taskNotification', '6.0.27');

COMMIT;

