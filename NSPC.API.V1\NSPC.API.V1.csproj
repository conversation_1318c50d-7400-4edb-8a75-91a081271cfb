<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <RootNamespace>NSPC.API.V1</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DotNetCore.CAP" Version="8.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.27" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.27" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.27">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.35.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.16" />
    <PackageReference Include="MongoDB.Bson" Version="2.24.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.24.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.8.1" />
    <PackageReference Include="Quartz.AspNetCore" Version="3.8.1" />
    <PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.8.1" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.1.0-dev-00943" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NSPC.Business\NSPC.Business.csproj" />
    <ProjectReference Include="..\NSPC.Common\NSPC.Common.csproj" />
    <ProjectReference Include="..\NSPC.Data\NSPC.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\CashbookTransaction\" />
    <Folder Include="Controllers\WorkingDay\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Controllers\WorkingDay\WorkingDayController.cs" />
    <Compile Remove="Controllers\KhachHang\LichSuChamSocConTroller.cs" />
  </ItemGroup>

</Project>
